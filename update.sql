INSERT INTO roles (id, name, identifier)
VALUES ('a182b477-a674-49ca-80f0-fe7f2286c992', '超级管理员', 'super_admin');
INSERT INTO organizations (
    id,
    name,
    code,
    type,
    contact_person,
    contact_phone,
    contact_email
) VALUES (
    'e8bd89c6-4b6a-4483-a045-06840f175632',  -- 替换为你自己的 UUID
    '个人付费机构',
    'bran',
    '事业单位',
    'bran.qiu',
    '18616606125',
    '<EMAIL>'
);
INSERT INTO roles (id, name, identifier, organization_id)
VALUES ('9669897e-b2a9-46bb-bc6b-cc5436295786', '机构管理员', 'admin', 'e8bd89c6-4b6a-4483-a045-06840f175632');
INSERT INTO roles (id, name, identifier, organization_id)
VALUES ('e49d69af-b66a-47f5-a11a-067760e36335', '普通用户', 'client', 'e8bd89c6-4b6a-4483-a045-06840f175632');
INSERT INTO users (
    id,
    username,
    hashed_password,
    role_id,
    organization_id
) VALUES (
    'e49d69af-b66a-47f5-a11a-067760e36335',
    'bran_admin',                          
    '$2b$12$sczGe./9SFtUBXXjnkb2U.MYVGJoMZSwUdDTTuKrAu/9EGtBRJUom',
    '9669897e-b2a9-46bb-bc6b-cc5436295786',
    'e8bd89c6-4b6a-4483-a045-06840f175632' 
);
UPDATE users
SET role_id = 'a182b477-a674-49ca-80f0-fe7f2286c992'
WHERE username = 'idea_admin';
UPDATE users
SET
    organization_id = 'e8bd89c6-4b6a-4483-a045-06840f175632',
    role_id = 'e49d69af-b66a-47f5-a11a-067760e36335'
WHERE username NOT IN ('idea_admin', 'bran_admin');

