.
├── Dockerfile
├── README.md
├── app
│   ├── __init__.py
│   ├── api
│   │   ├── deps.py
│   │   ├── routes
│   │   │   ├── __init__.py
│   │   │   ├── api_keys.py
│   │   │   ├── area.py
│   │   │   ├── auth.py
│   │   │   ├── dictionary.py
│   │   │   ├── menu.py
│   │   │   ├── model_configs.py
│   │   │   ├── organization_role_menu.py
│   │   │   ├── organizations.py
│   │   │   ├── outline.py
│   │   │   ├── project_configs.py
│   │   │   ├── project_downloads.py
│   │   │   ├── project_leaders.py
│   │   │   ├── project_member_joins.py
│   │   │   ├── project_members.py
│   │   │   ├── project_model_config.py
│   │   │   ├── project_report.py
│   │   │   ├── requirements_attachment_files.py
│   │   │   ├── research.py
│   │   │   ├── role.py
│   │   │   ├── user_report_usages.py
│   │   │   ├── users.py
│   │   │   └── workflow.py
│   │   └── schemas
│   │       ├── __init__.py
│   │       ├── area.py
│   │       ├── dictionary.py
│   │       ├── llm_token_response.py
│   │       ├── menu.py
│   │       ├── model_config.py
│   │       ├── organization_role_menu.py
│   │       ├── organizations.py
│   │       ├── project_configs.py
│   │       ├── project_leaders.py
│   │       ├── project_member_joins.py
│   │       ├── project_members.py
│   │       ├── project_model_config.py
│   │       ├── report.py
│   │       ├── requirements_attachment_files.py
│   │       ├── role.py
│   │       ├── user.py
│   │       ├── user_report_usage.py
│   │       └── workflow.py
│   ├── core
│   │   ├── config.py
│   │   ├── logging.py
│   │   └── security.py
│   ├── db
│   │   └── config.py
│   ├── main.py
│   ├── models
│   │   ├── __init__.py
│   │   ├── area.py
│   │   ├── dictionary.py
│   │   ├── literature_library.py
│   │   ├── menu.py
│   │   ├── model_config.py
│   │   ├── organization_role_menu.py
│   │   ├── organizations.py
│   │   ├── project_configs.py
│   │   ├── project_leaders.py
│   │   ├── project_member_joins.py
│   │   ├── project_members.py
│   │   ├── project_model_config.py
│   │   ├── report.py
│   │   ├── requirements_attachments_files.py
│   │   ├── research.py
│   │   ├── role.py
│   │   ├── user.py
│   │   ├── user_report_usage.py
│   │   └── workflow.py
│   ├── services
│   │   ├── __init__.py
│   │   ├── attachments_service.py
│   │   ├── llm_service.py
│   │   ├── llm_token_service.py
│   │   ├── product_configs_service.py
│   │   ├── prompts.py
│   │   ├── pubmed_service.py
│   │   ├── report_prompts.py
│   │   ├── research_service.py
│   │   └── search_service.py
│   └── utils
│       ├── content_manager.py
│       ├── crypto.py
│       ├── llm_service.py
│       ├── utils.py
│       └── validate_error.py
├── build.sh
├── deploy.sh
├── directories.md
├── docker-compose-localdb.yml
├── docker-compose.yml
├── entrypoint.sh
├── migrations
│   └── models
├── pyproject.toml
├── remote_db.dump
├── requirements.txt
├── run-localdb.md
├── run.py
├── scripts
│   ├── area.json
│   ├── data.json
│   ├── import_area.py
│   ├── input.py
│   └── output.py
├── update.sql
└── wsgi.py