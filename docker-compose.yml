version: '3.8'

services:
  api:
    image: hi-researcher-api-${ENV:-dev}:${VERSION:-latest}
    container_name: ${CONTAINER_NAME:-hi-researcher-api-${ENV:-dev}}
    restart: "no"
    ports:
      - ${PORT}:${PORT}
    volumes:
      - ${STATICS_PATH}:/app/llm_file
    env_file:
      - ${ENV_FILE}
    networks:
      - api-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - LOG_LEVEL=DEBUG

networks:
  api-network:
    driver: bridge
