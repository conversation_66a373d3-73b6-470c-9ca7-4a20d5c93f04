# 服务配置
HOST=0.0.0.0
PORT=8000


# 数据库配置
DATABASE_URL=postgres://postgres:123456@localhost:5432/hi_ideagen

# 报告和大纲保存的文件夹
STATICS_PATH=/home/<USER>/hi-ideagen-statics

# 安全配置，用于生成JWT令牌，请勿泄露，请勿泄露，请勿泄露，用于加密和安全处理的密钥，相当于盐值
SECRET_KEY=

# 默认管理员配置
DEFAULT_ADMIN_USERNAME=
DEFAULT_ADMIN_PASSWORD=
DEFAULT_ADMIN_ROLE=

# API密钥前缀
API_KEY_PREFIX=sk-

# 研究配置
# Google API Key
# GOOGLE_API_KEY=
# Custom Search Engine ID，用于Google搜索
# CUSTOM_SEARCH_ENGINE_ID=
# SerpAPI API Key，用于Google搜索
SERPAPI_API_KEY=
# Jina API Key，用于Jina搜索
JINA_API_KEY=

# 搜索配置
SEARCH_RESULTS_LIMIT=10
# 研究迭代次数限制（搜索-分析-改进查询的最大循环次数，每次循环系统会根据已获取的信息生成新的搜索查询，直到达到信息充分或达到迭代上限）
RESEARCH_ITERATION_LIMIT=2

# 报告配置
# 报告最小字数
REPORT_MIN_WORDS=1000
# 报告最大字数
REPORT_MAX_WORDS=5000

# 日志配置
# 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO