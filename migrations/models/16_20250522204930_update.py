from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "knowledge_canvas" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(255) NOT NULL,
    "source_type" VARCHAR(100) NOT NULL,
    "type" VARCHAR(100) NOT NULL,
    "summary" TEXT NOT NULL,
    "key_notes" TEXT NOT NULL,
    "related_notes" TEXT NOT NULL,
    "ai_questions" TEXT NOT NULL,
    "image_url" VARCHAR(500),
    "original_article" TEXT NOT NULL,
    "ai_outline" TEXT NOT NULL,
    "user_notes" TEXT NOT NULL,
    "note_update_at" TIMESTAMPTZ,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "user_id" UUID REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "knowledge_canvas"."name" IS '名称';
COMMENT ON COLUMN "knowledge_canvas"."source_type" IS '来源类型';
COMMENT ON COLUMN "knowledge_canvas"."type" IS '类型';
COMMENT ON COLUMN "knowledge_canvas"."summary" IS '概要';
COMMENT ON COLUMN "knowledge_canvas"."key_notes" IS '重点注释';
COMMENT ON COLUMN "knowledge_canvas"."related_notes" IS '关联笔记';
COMMENT ON COLUMN "knowledge_canvas"."ai_questions" IS 'AI提问';
COMMENT ON COLUMN "knowledge_canvas"."image_url" IS '图片URL';
COMMENT ON COLUMN "knowledge_canvas"."original_article" IS '原始文章';
COMMENT ON COLUMN "knowledge_canvas"."ai_outline" IS 'AI大纲';
COMMENT ON COLUMN "knowledge_canvas"."user_notes" IS '用户注释';
COMMENT ON COLUMN "knowledge_canvas"."note_update_at" IS '笔记修改时间';
COMMENT ON COLUMN "knowledge_canvas"."created_at" IS '创建时间';
COMMENT ON COLUMN "knowledge_canvas"."updated_at" IS '更新时间';
COMMENT ON COLUMN "knowledge_canvas"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "knowledge_canvas"."user_id" IS '创建用户';
COMMENT ON TABLE "knowledge_canvas" IS '知识画布模型';
        CREATE TABLE IF NOT EXISTS "knowledge_canvas_tags" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(50) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "user_id" UUID REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "knowledge_canvas_tags"."name" IS '标签名称';
COMMENT ON COLUMN "knowledge_canvas_tags"."created_at" IS '创建时间';
COMMENT ON COLUMN "knowledge_canvas_tags"."updated_at" IS '更新时间';
COMMENT ON COLUMN "knowledge_canvas_tags"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "knowledge_canvas_tags"."user_id" IS '创建用户';
COMMENT ON TABLE "knowledge_canvas_tags" IS '知识画布标签模型';
        CREATE TABLE IF NOT EXISTS "organization_menus" (
    "id" UUID NOT NULL PRIMARY KEY,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "menu_id" UUID NOT NULL REFERENCES "menus" ("id") ON DELETE CASCADE,
    "organization_id" UUID NOT NULL REFERENCES "organizations" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "organization_menus"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organization_menus"."created_at" IS '创建时间';
COMMENT ON COLUMN "organization_menus"."updated_at" IS '更新时间';
COMMENT ON COLUMN "organization_menus"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organization_menus"."menu_id" IS '菜单';
COMMENT ON COLUMN "organization_menus"."organization_id" IS '机构';
COMMENT ON TABLE "organization_menus" IS '机构菜单关联表';
        ALTER TABLE "researches" ALTER COLUMN "literatures" DROP DEFAULT;
        ALTER TABLE "researches" ALTER COLUMN "literatures" DROP NOT NULL;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "researches" ALTER COLUMN "literatures" SET NOT NULL;
        ALTER TABLE "researches" ALTER COLUMN "literatures" SET;
        DROP TABLE IF EXISTS "knowledge_canvas";
        DROP TABLE IF EXISTS "organization_menus";
        DROP TABLE IF EXISTS "knowledge_canvas_tags";"""
