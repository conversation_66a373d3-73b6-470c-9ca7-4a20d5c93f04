import json
import aiohttp
import asyncio
import traceback
from typing import Dict, List, Optional, AsyncGenerator, Any, Callable
import os

from app.core.config import settings
from app.core.logging import get_logger

# 获取logger实例
logger = get_logger(__name__)


async def call_llm(
    messages: List[Dict[str, str]], 
    flag: str = "default", # 调用的目的，用于区分不同的调用场景
    model: str = "", 
    stream: bool = False,
    apiKey: str = "",
    apiUrl: str = "",
) -> Optional[str]:
    """
    调用OpenRouter LLM API
    
    Args:
        messages: 聊天消息列表
        model: 模型名称
        stream: 是否流式响应
    
    Returns:
        模型响应文本，如果失败则返回None
    """
    logger.info(f"调用LLM，模型: {model}, 目的: {flag}")
    if not apiKey:
        error = "未设置API_KEY环境变量"
        logger.error(error)
        raise ValueError(error)
        
    headers = {
        "Authorization": f"Bearer {apiKey}",
        "X-Title": "Hi-IdeaGen",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": messages,
        "stream": stream
    }
    
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"LLM请求，超时设置: {timeout.total}秒，消息数量: {len(messages)}")
    
    try:
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送请求: {apiUrl}")
            async with session.post(
                apiUrl,
                headers=headers,
                json=payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"OpenRouter API 错误: 状态码 {resp.status}")
                    logger.error(f"错误详情: {error_text}")
                    return None
                
                # 处理非流式响应
                if not stream:
                    logger.debug("接收非流式LLM响应")
                    result = await resp.json()
                    logger.debug(f"完整响应: {result}")
                    
                    try:
                        # 检查OpenRouter返回的标准响应格式
                        if 'choices' in result and len(result['choices']) > 0:
                            if 'message' in result['choices'][0] and 'content' in result['choices'][0]['message']:
                                content = result['choices'][0]['message']['content']
                                logger.info(f"LLM响应成功(标准格式)，内容长度: {len(content)}")
                                return content
                        
                        # 检查替代响应格式: Claude格式
                        if 'content' in result:
                            content = result['content']
                            logger.info(f"LLM响应成功(Claude格式)，内容长度: {len(content)}")
                            return content
                            
                        # 检查替代响应格式: Gemini格式
                        if 'candidates' in result and len(result['candidates']) > 0:
                            if 'content' in result['candidates'][0] and 'parts' in result['candidates'][0]['content']:
                                text_parts = [part.get('text', '') for part in result['candidates'][0]['content']['parts'] if 'text' in part]
                                content = ''.join(text_parts)
                                logger.info(f"LLM响应成功(Gemini格式)，内容长度: {len(content)}")
                                return content
                        
                        # 检查替代响应格式: 纯文本
                        if isinstance(result, str):
                            logger.info(f"LLM响应成功(纯文本格式)，内容长度: {len(result)}")
                            return result
                            
                        # 如果都无法解析，尝试将整个响应转换为字符串
                        logger.warning(f"无法解析的响应格式，尝试转换为字符串: {result}")
                        
                        # 检查是否为Google AI的位置限制错误
                        if isinstance(result, dict) and 'error' in result:
                            error_data = result['error']
                            # 检查是否包含provider_name和Google位置限制信息
                            if 'metadata' in error_data and 'provider_name' in error_data['metadata']:
                                provider = error_data['metadata'].get('provider_name', '')
                                raw_error = error_data['metadata'].get('raw', '')
                                
                                if provider == 'Google AI Studio' and 'User location is not supported' in raw_error:
                                    logger.error(f"Google AI Studio 位置限制错误: {raw_error}")
                                    return "由于地理位置限制，无法访问Google AI服务。正在尝试其他方法解析内容..."
                            raise ValueError(result)
                        return json.dumps(result)
                            
                    except (KeyError, IndexError) as e:
                        logger.error(f"OpenRouter 响应结构异常: {str(e)}")
                        logger.debug(f"完整响应: {result}")
                        
                        # 尝试找到任何可能的文本内容
                        if isinstance(result, dict):
                            # 递归搜索字典中的任何文本内容
                            def extract_text(obj):
                                if isinstance(obj, str):
                                    return obj
                                elif isinstance(obj, dict):
                                    for k, v in obj.items():
                                        if k in ['content', 'text', 'message', 'response']:
                                            text = extract_text(v)
                                            if text:
                                                return text
                                    for v in obj.values():
                                        text = extract_text(v)
                                        if text:
                                            return text
                                elif isinstance(obj, list):
                                    for item in obj:
                                        text = extract_text(item)
                                        if text:
                                            return text
                                return None
                            
                            extracted_text = extract_text(result)
                            if extracted_text:
                                logger.info(f"从异常响应中提取到文本，长度: {len(extracted_text)}")
                                return extracted_text
                        
                        # 如果实在无法提取，返回整个响应的字符串形式
                        logger.warning("无法从响应中提取文本，返回整个响应的字符串形式")
                        return str(result)
                
                # 流式响应，返回完整内容
                else:
                    logger.debug("开始接收流式LLM响应")
                    full_response = ""
                    chunk_count = 0
                    async for line in resp.content:
                        line = line.decode('utf-8').strip()
                        if not line:
                            continue
                        
                        # 跳过 "data: " 前缀
                        if line.startswith("data: "):
                            line = line[6:]
                        
                        # 处理流结束标记
                        if line == "[DONE]":
                            logger.debug("流式响应结束")
                            break
                        
                        try:
                            # 解析 JSON 数据
                            data = json.loads(line)
                            logger.debug(f"流式响应数据: {data}")
                            
                            # 提取内容增量 - 标准格式
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    chunk = delta['content']
                                    full_response += chunk
                                    chunk_count += 1
                            # Claude格式
                            elif 'content' in data:
                                chunk = data['content']
                                full_response += chunk
                                chunk_count += 1
                            # Gemini格式
                            elif 'candidates' in data and len(data['candidates']) > 0:
                                if 'content' in data['candidates'][0] and 'parts' in data['candidates'][0]['content']:
                                    text_parts = [part.get('text', '') for part in data['candidates'][0]['content']['parts'] if 'text' in part]
                                    chunk = ''.join(text_parts)
                                    full_response += chunk
                                    chunk_count += 1
                            # 任何可能包含文本的字段
                            else:
                                for key in ['text', 'message', 'response', 'output']:
                                    if key in data:
                                        chunk = data[key] if isinstance(data[key], str) else str(data[key])
                                        full_response += chunk
                                        chunk_count += 1
                                        break
                        except json.JSONDecodeError:
                            logger.error(f"无法解析 JSON: {line}")
                        except Exception as e:
                            logger.error(f"处理流式响应时出错: {str(e)}")
                    
                    logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                    return full_response
                    
    except asyncio.TimeoutError:
        logger.error(f"OpenRouter API 请求超时 ({timeout.total} 秒)")
        return None
    except aiohttp.ClientError as e:
        logger.error(f"aiohttp 客户端错误: {e.__class__.__name__}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"调用 OpenRouter 时发生错误: {e.__class__.__name__}: {str(e)}")
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        return None


async def stream_llm_response(
    messages: List[Dict[str, str]], 
    model: str = "",
    apiKey: str = "",
    apiUrl: str = "",
) -> AsyncGenerator[bytes, None]:
    """
    流式调用LLM并返回SSE格式的响应
    
    Args:
        messages: 聊天消息列表
        model: 模型名称
    
    Yields:
        SSE格式的响应数据
    """
    logger.info(f"流式调用LLM，模型: {model}")
    if not apiKey:
        logger.error("未设置API_KEY环境变量")
        yield f"data: {json.dumps({'status': 'error', 'message': '未设置API_KEY环境变量'})}\n\n".encode('utf-8')
        return
        
    headers = {
        "Authorization": f"Bearer {apiKey}",
        "X-Title": "Hi-IdeaGen",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": messages,
        "stream": True
    }
    
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"流式LLM请求，超时设置: {timeout.total}秒，消息数量: {len(messages)}")
    
    try:
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送流式请求: {apiUrl}")
            async with session.post(
                apiUrl,
                headers=headers,
                json=payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    error_msg = f"OpenRouter API 错误: 状态码 {resp.status}, 错误详情: {error_text}"
                    logger.error(error_msg)
                    
                    # 如果是位置限制等特定错误，给用户更友好的提示
                    if "User location is not supported" in error_text:
                        friendly_msg = "由于地理位置限制，无法访问当前选择的AI模型。请稍后再试或联系管理员更换模型。"
                        yield f"data: {json.dumps({'status': 'error', 'message': friendly_msg})}\n\n".encode('utf-8')
                    else:
                        yield f"data: {json.dumps({'status': 'error', 'message': error_msg})}\n\n".encode('utf-8')
                    return
                
                # 直接转发流式响应
                chunk_count = 0
                logger.debug("开始接收并转发流式LLM响应")
                async for line in resp.content:
                    try:
                        line_str = line.decode('utf-8').strip()
                        if not line_str:
                            continue
                        
                        # 跳过OpenRouter处理状态消息
                        if ": OPENROUTER PROCESSING" in line_str:
                            logger.debug(f"跳过OpenRouter处理状态消息: {line_str}")
                            continue
                        
                        # 直接传递 SSE 格式的数据
                        if line_str.startswith("data: "):
                            # 检查数据内容
                            data_content = line_str[6:].strip()
                            if data_content and data_content not in ["[DONE]", "null"]:
                                yield line
                                chunk_count += 1
                        else:
                            # 检查是否是直接的内容（非JSON）
                            if not line_str.startswith("{") and not line_str.startswith("["):
                                # 直接包装为SSE格式发送
                                yield f"data: {json.dumps({'choices': [{'delta': {'content': line_str}}]})}\n\n".encode('utf-8')
                                chunk_count += 1
                            else:
                                # 确保格式正确
                                yield f"data: {line_str}\n\n".encode('utf-8')
                                chunk_count += 1
                    except UnicodeDecodeError:
                        logger.warning("处理响应时遇到解码错误，跳过此块")
                        continue
                    except Exception as e:
                        logger.error(f"处理响应块时出错: {str(e)}")
                        continue
                
                # 流结束
                logger.info(f"流式响应转发完成，转发了 {chunk_count} 个数据块")
                yield f"data: {json.dumps({'status': 'completed'})}\n\n".encode('utf-8')
                    
    except asyncio.TimeoutError:
        logger.error(f"OpenRouter API 流式请求超时 ({timeout.total} 秒)")
        yield f"data: {json.dumps({'status': 'error', 'message': f'请求超时，请稍后再试 ({timeout.total} 秒)'})}\n\n".encode('utf-8')
    except aiohttp.ClientError as e:
        logger.error(f"流式请求客户端错误: {str(e)}")
        yield f"data: {json.dumps({'status': 'error', 'message': f'网络连接错误，请检查网络并稍后再试'})}\n\n".encode('utf-8')
    except Exception as e:
        logger.error(f"流式调用 OpenRouter 时发生错误: {str(e)}")
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        yield f"data: {json.dumps({'status': 'error', 'message': f'系统遇到技术问题，请稍后再试'})}\n\n".encode('utf-8')


async def stream_llm_and_save(
    messages: List[Dict[str, str]],
    file_path: str,
    model: str = "",
    callback: Optional[Callable[[str], None]] = None,
    complete_callback: Optional[Callable[[], None]] = None,
    apiKey: str = "",
    apiUrl: str = ""
) -> str:
    """
    流式调用LLM，实时保存响应内容到文件，并返回完整响应
    
    Args:
        messages: 聊天消息列表
        file_path: 文件路径（绝对路径或相对路径）
        model: 模型名称
        callback: 可选的回调函数，每接收到一个chunk就调用一次
        complete_callback: 可选的完成回调函数，生成完成时调用
    
    Returns:
        完整的模型响应文本
    """
    logger.info(f"流式调用LLM并保存到文件，模型: {model}, 文件: {file_path}")
    if not apiKey:
        error = "未设置API_KEY环境变量"
        logger.error(error)
        raise ValueError(error)
        
    headers = {
        "Authorization": f"Bearer {apiKey}",
        "X-Title": "Hi-IdeaGen",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": messages,
        "stream": True,
        "max_tokens": 100
    }
    
    # 处理文件路径
    if os.path.isabs(file_path):
        abs_file_path = file_path
    else:
        abs_file_path = os.path.join(os.getcwd(), file_path)
    
    # 确保目录存在
    directory = os.path.dirname(abs_file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
    
    # 如果文件存在，先清空文件内容
    if os.path.exists(abs_file_path):
        with open(abs_file_path, "w", encoding="utf-8") as f:
            pass
    
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"流式LLM请求，超时设置: {timeout.total}秒，消息数量: {len(messages)}")
    
    try:
        full_response = ""
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送流式请求: {apiUrl}")
            async with session.post(
                apiUrl,
                headers=headers,
                json=payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    error_msg = f"OpenRouter API 错误: 状态码 {resp.status}, 错误详情: {error_text}"
                    logger.error(error_msg)
                    # 错误信息写入文件
                    with open(abs_file_path, "w", encoding="utf-8") as f:
                        f.write(f"API请求错误: {error_msg}")
                    return f"API请求错误: {error_msg}"
                
                # 流式响应并写入文件
                logger.debug(f"开始接收流式LLM响应并写入文件: {abs_file_path}")
                chunk_count = 0
                response_debug = []  # 用于调试的响应样本
                
                # 使用追加模式写入文件
                with open(abs_file_path, "a", encoding="utf-8") as f:
                    try:
                        async for line in resp.content:
                            try:
                                line_str = line.decode('utf-8').strip()
                                if not line_str:
                                    continue
                                
                                # 收集一些样本用于调试
                                if len(response_debug) < 3:
                                    response_debug.append(line_str)
                                
                                # 跳过 "data: " 前缀
                                if line_str.startswith("data: "):
                                    line_str = line_str[6:]
                                
                                # 处理流结束标记
                                if line_str == "[DONE]":
                                    logger.debug("流式响应结束")
                                    break
                                
                                # 解析 JSON 数据
                                try:
                                    data = json.loads(line_str)
                                    
                                    # 提取内容增量
                                    if 'choices' in data and len(data['choices']) > 0:
                                        delta = data['choices'][0].get('delta', {})
                                        if 'content' in delta and delta['content']:
                                            chunk = delta['content']
                                            full_response += chunk
                                            
                                            # 实时写入文件
                                            f.write(chunk)
                                            f.flush()
                                            
                                            # 如果有回调函数，则调用
                                            if callback:
                                                callback(chunk)
                                            
                                            chunk_count += 1
                                except json.JSONDecodeError as je:
                                    logger.error(f"无法解析 JSON '{line_str[:100]}...': {str(je)}")
                                except Exception as e:
                                    logger.error(f"处理响应数据时出错: {str(e)}, 数据: {line_str[:100]}...")
                            except UnicodeDecodeError as ue:
                                logger.error(f"解码响应时出错: {str(ue)}")
                            except Exception as e:
                                logger.error(f"处理响应行时出错: {str(e)}")
                                
                    except Exception as e:
                        logger.error(f"读取响应流时出错: {str(e)}")
                        # 如果出错，至少保存已收集的内容
                        if full_response:
                            f.write(f"\n\n[接收错误: {str(e)}]")
                            f.flush()
                
                logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                
                # 如果没有收到任何内容，记录更多的调试信息
                if not full_response:
                    logger.warning(f"没有接收到有效内容。响应样本: {response_debug}")
                    # 写入一些信息到文件，表明请求成功但没有收到内容
                    with open(abs_file_path, "w", encoding="utf-8") as f:
                        f.write(f"请求完成，但未收到有效内容。响应样本: {response_debug}")
                    return f"请求完成，但未收到有效内容"
                
                # 调用完成回调
                if complete_callback:
                    try:
                        await complete_callback()
                    except Exception as e:
                        logger.error(f"执行完成回调时出错: {str(e)}")
                
                return full_response
                    
    except asyncio.TimeoutError as te:
        error_msg = f"OpenRouter API 请求超时 ({timeout.total} 秒)"
        logger.error(error_msg)
        with open(abs_file_path, "w", encoding="utf-8") as f:
            f.write(f"请求超时: {error_msg}")
        return f"请求超时: {error_msg}"
    except aiohttp.ClientError as ce:
        error_msg = f"HTTP客户端错误: {str(ce)}"
        logger.error(error_msg)
        with open(abs_file_path, "w", encoding="utf-8") as f:
            f.write(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"调用OpenRouter时发生错误: {str(e)}"
        logger.error(error_msg)
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        with open(abs_file_path, "w", encoding="utf-8") as f:
            f.write(error_msg)
        return error_msg 