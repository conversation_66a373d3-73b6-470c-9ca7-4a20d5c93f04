from typing import Optional
from app.core.logging import get_logger
from app.services.llm_service import call_llm
from app.utils.utils import send_data, ResponseModel
from app.models.model_config import ModelConfig
# from app.models.user import User
from app.api.schemas.user import UserResponse
from app.services.prompts import generate_optimize_project_name_prompt
import json
from uuid import UUID
# 获取logger实例
logger = get_logger(__name__)
class ProductConfigsService:
    
    async def get_default_model(self, user: UserResponse) -> Optional[ModelConfig]:
        """
        获取用户默认的模型配置
        
        Args:
            user: 用户对象
            
        Returns:
            Optional[ModelConfig]: 用户的默认模型配置，如果不存在则返回None
        """
        try:
            # 获取用户启用的模型配置
            model = await ModelConfig.filter(
                user_id=user.id,
                is_active=True,
                is_deleted=False
            ).first()
            return model
        except Exception as e:
            return None

    async def optimize_project_name(
        self, 
        project_configs_name: str,
        user: UserResponse,
        model_config_id: Optional[UUID] = None
    ) -> ResponseModel[str]:
        """
        使用LLM模型优化项目名称
        
        Args:
            project_configs_name: 原始项目名称
            user: 用户对象
            model_config_id: 可选的模型配置ID，如果提供则使用指定的模型配置
            
        Returns:
            ResponseModel[str]: 包含优化后的项目名称的响应
        """
        try:
            # 获取模型配置，优先使用指定的模型配置ID
            model_config = None
            if model_config_id:
                model_config = await ModelConfig.filter(
                    id=model_config_id,
                    user_id=user.id,
                    is_deleted=False
                ).first()
            
            # 如果没有指定模型配置ID或指定的模型配置不存在，则使用默认模型配置
            if not model_config:
                model_config = await self.get_default_model(user)
                
            if not model_config:
                return send_data(False, None, "未找到用户可用的模型配置")
            
            # 使用prompts.py中的函数生成提示词
            messages = generate_optimize_project_name_prompt(project_configs_name)
            
            # 调用LLM服务
            optimized_name = await call_llm(
                messages=messages,
                flag="优化申报名称",
                model=model_config.model_name,
                apiKey=model_config.api_key,
                apiUrl=model_config.api_url
            )
            
            if not optimized_name:
                # 当结果为空时，尝试再次调用LLM
                logger.info("LLM服务返回为空，尝试再次调用")
                optimized_name = await call_llm(
                    messages=messages,
                    flag="优化申报名称-重试",
                    model=model_config.model_name,
                    apiKey=model_config.api_key,
                    apiUrl=model_config.api_url
                )
                
                if not optimized_name:
                    return send_data(False, None, "LLM服务调用返回为空")
            
            # 清理返回结果
            optimized_name = optimized_name.strip()
            logger.info(f"优化后的项目名称: {optimized_name}")
            # 检查返回结果格式
            # 如果不是JSON数组格式，则将整个返回内容放入数组中返回
            try:
                # 尝试解析为JSON
                json_data = json.loads(optimized_name)
                # 如果已经是数组格式，直接使用
                if isinstance(json_data, list):
                    return send_data(True, optimized_name)
            except:
                 logger.info(f"处理失败的优化后的项目名称: {optimized_name}")
                 
                 # 处理可能带有```json```标记的内容
                 if "```json" in optimized_name and "```" in optimized_name:
                     # 提取```json和最后一个```之间的内容
                     json_start = optimized_name.find("```json") + 7
                     json_end = optimized_name.rfind("```")
                     if json_start < json_end:
                         json_content = optimized_name[json_start:json_end].strip()
                         try:
                             # 尝试解析提取的JSON内容
                             json_data = json.loads(json_content)
                             if isinstance(json_data, list):
                                 return send_data(True, json.dumps(json_data, ensure_ascii=False))
                             else:
                                 # 如果不是列表，包装成列表
                                 return send_data(True, json.dumps([json_data], ensure_ascii=False))
                         except:
                             # 如果提取的内容不是有效JSON，则按原方式处理
                             logger.info(f"提取的JSON内容无效: {json_content}")
                 
                 # 处理\boxed{}格式的内容
                 elif "\\boxed{" in optimized_name and "}" in optimized_name:
                     # 提取\boxed{}中的内容
                     boxed_start = optimized_name.find("\\boxed{") + 7
                     boxed_end = optimized_name.rfind("}")
                     if boxed_start < boxed_end:
                         boxed_content = optimized_name[boxed_start:boxed_end].strip()
                         try:
                             # 尝试解析提取的内容
                             json_data = json.loads(boxed_content)
                             if isinstance(json_data, list):
                                 return send_data(True, json.dumps(json_data, ensure_ascii=False))
                             else:
                                 # 如果不是列表，包装成列表
                                 return send_data(True, json.dumps([json_data], ensure_ascii=False))
                         except:
                             # 如果不是有效JSON，记录日志
                             logger.info(f"提取的\\boxed内容无效: {boxed_content}")
                 
                 # 默认处理方式：将整个内容包装为JSON数组
                 optimized_name = json.dumps([optimized_name], ensure_ascii=False)
            
            return send_data(True, optimized_name)
            
        except Exception as e:
            return send_data(False, None, f"优化项目名称失败: {str(e)}") 