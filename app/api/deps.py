from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON>2<PERSON><PERSON>wordBearer
from jose import jwt, JWTError

from app.core.config import settings
from app.models.user import User
from app.api.schemas.user import UserResponse
import json
from pydantic import BaseModel
from typing import Optional
from app.api.schemas.role import InsetRole
from app.models.organizations import Organizations


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login")

class UserInfo(BaseModel):
    username: str
    organization_code: Optional[str] = None
    class Config:
        from_attribute= True

async def get_current_user(token: str = Depends(oauth2_scheme)) -> UserResponse:
    """通过JWT令牌获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    user_info: Optional[UserInfo] = None
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        user_info = UserInfo.model_validate(json.loads(payload.get("sub")))
        if user_info.username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = await User.filter(username=user_info.username, is_deleted=False).prefetch_related('role', 'organization').first()
    user_result = UserResponse.model_validate(user)
    return user_result

