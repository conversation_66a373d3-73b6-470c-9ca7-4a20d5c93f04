from fastapi import APIRouter, Depends
from app.api.routes import (
  auth,
  requirements_attachment_files,
  users,
  project_configs,
  project_leaders,
  project_members,
  project_member_joins,
  model_configs,
  project_report,
  project_downloads,
  outline,
  user_report_usages,
  workflow,
  project_model_config,
  dictionary,
  area,
  research,
  organizations,
  menu,
  role,
  organization_role_menu,
  insight,
  organization_menu
)
from app.api.deps import get_current_user


api_router = APIRouter(prefix="/api")
auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user)])

# 添加各模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
auth_router.include_router(role.router, prefix="/role", tags=["角色管理"])
auth_router.include_router(menu.router, prefix="/menu", tags=["菜单管理"])
auth_router.include_router(organization_menu.router, prefix="/organization-menu", tags=["机构菜单管理"])
auth_router.include_router(organization_role_menu.router, prefix="/role-menu", tags=["角色菜单管理"]) 
auth_router.include_router(users.router, prefix="/users", tags=["用户管理"])
auth_router.include_router(research.router, prefix="/research", tags=["调研信息"])
auth_router.include_router(project_configs.router, prefix="/project-configs", tags=["项目配置"])
auth_router.include_router(project_leaders.router, prefix="/project-leaders", tags=["项目主体"]) 
auth_router.include_router(project_members.router, prefix="/project-members", tags=["项目成员"])
auth_router.include_router(project_member_joins.router, prefix="/project-member-joins", tags=["项目成员关联"])
auth_router.include_router(project_report.router, prefix="/project-reports", tags=["项目大纲报告生成"])
auth_router.include_router(project_downloads.router, prefix="/project-downloads", tags=["项目文件下载"])
auth_router.include_router(requirements_attachment_files.router, prefix="/requirements-attachments-files", tags=["申报要求附件文件"])
auth_router.include_router(user_report_usages.router, tags=["用户报告使用次数"])
auth_router.include_router(workflow.router, prefix="/workflows", tags=["工作流程"])
# auth_router.include_router(api_keys.router, prefix="/api-keys", tags=["API密钥管理"])
auth_router.include_router(model_configs.router, prefix="/model-configs", tags=["模型配置管理"]) 
auth_router.include_router(outline.router, prefix="/text-handle", tags=["文本处理"])
auth_router.include_router(project_model_config.router, prefix="/project-model-configs", tags=["项目模型配置关联"])
auth_router.include_router(dictionary.router, prefix="/dictionary", tags=["字典"])
auth_router.include_router(area.router, prefix="/area", tags=["省市区"])
auth_router.include_router(organizations.router, prefix="/organizations", tags=["机构管理"])
# auth_router.include_router(organization_role_menu.router, prefix="/organization-role-menus", tags=["机构角色菜单管理"])
auth_router.include_router(insight.router, prefix="/insight")
api_router.include_router(auth_router)
