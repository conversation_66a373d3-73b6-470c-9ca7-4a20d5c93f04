from fastapi import APIRouter, Depends, Query, status
from typing import List, Optional
from uuid import UUID
from datetime import datetime

from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.api.schemas.organization_menu import (
    OrganizationMenuBatchCreate,
    OrganizationMenuResponse
)
from app.models.organization_menu import OrganizationMenu
from app.models.organizations import Organizations
from app.models.menu import Menu
from app.api.schemas.role import InsetRole
from app.api.schemas.menu import MenuTreeResponse
from app.utils.utils import send_data, ResponseModel
from app.models.organization_role_menu import OrganizationRoleMenu
from app.models.role import Role

router = APIRouter()

@router.post("/batch", response_model=ResponseModel[int], status_code=status.HTTP_201_CREATED, summary="给机构赋予菜单列表【超级管理员】")
async def create_organization_menus_batch(
    data: OrganizationMenuBatchCreate,
    current_user: UserResponse = Depends(get_current_user)
):
    """给机构赋予菜单列表"""
    # 只有超级管理员可以执行此操作
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        return send_data(False, None, "权限不足")
    
    try:
        # 检查机构是否存在
        organization = await Organizations.filter(id=data.organization_id, is_deleted=False).first()
        if not organization:
            return send_data(False, None, "机构不存在")
        
        # 检查菜单是否存在
        menus = await Menu.filter(id__in=data.menu_ids, is_deleted=False).all()
        if len(menus) != len(data.menu_ids):
            return send_data(False, None, "部分菜单不存在")
        
        # 先将现有关联标记为删除
        await OrganizationMenu.filter(
            organization_id=data.organization_id,
            is_deleted=False
        ).update(is_deleted=True, deleted_at=datetime.now())
        
        # 创建新关联
        created_count = 0
        for menu_id in data.menu_ids:
            # 检查是否已存在但被标记为删除
            existing = await OrganizationMenu.filter(
                organization_id=data.organization_id,
                menu_id=menu_id
            ).first()
            
            if existing:
                # 如果存在则更新
                existing.is_deleted = False
                existing.deleted_at = None
                await existing.save()
            else:
                # 否则创建新的
                await OrganizationMenu.create(
                    organization_id=data.organization_id,
                    menu_id=menu_id
                )
            
            created_count += 1
        
        # 找出admin角色并清除其菜单权限
        admin_role = await Role.filter(
          organization_id=data.organization_id,
          identifier=InsetRole.ADMIN,
          is_deleted=False
        ).first()
        if admin_role:
            # 将admin角色的所有菜单关联标记为删除
            await OrganizationRoleMenu.filter(
                organization_id=data.organization_id,
                role_id=admin_role.id,
                is_deleted=False
            ).update(is_deleted=True, deleted_at=datetime.now())
            for menu_id in data.menu_ids:
                await OrganizationRoleMenu.create(
                    organization_id=data.organization_id,
                    role_id=admin_role.id,
                    menu_id=menu_id
                )    
        # 处理OrganizationRoleMenu表中的记录
        # 1. 获取该机构的所有角色菜单关联
        org_role_menus = await OrganizationRoleMenu.filter(
            organization_id=data.organization_id,
            is_deleted=False
        ).all()
        
        # 2. 遍历并检查每条记录的menu_id是否在data.menu_ids中
        for role_menu in org_role_menus:
            if role_menu.menu_id not in data.menu_ids:
                # 如果menu_id不在新的菜单列表中，则标记为删除
                role_menu.is_deleted = True
                role_menu.deleted_at = datetime.now()
                await role_menu.save()
                
        return send_data(True, created_count)
    except Exception as e:
        return send_data(False, None, f"批量创建机构菜单关联失败: {str(e)}")

@router.get("", response_model=ResponseModel[List[OrganizationMenuResponse]], summary="获取机构已经有的菜单列表（非树状）【超级管理员】")
async def list_organization_menus(
    organization_id: UUID = Query(..., description="按机构ID筛选"),
    current_user: UserResponse = Depends(get_current_user)
):
    """获取机构菜单关联列表(非树状)【超级管理员】"""
    # 只有超级管理员可以执行此操作
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        return send_data(False, None, "权限不足")
    
    try:
        query = await OrganizationMenu.filter(organization_id=organization_id, is_deleted=False).order_by("-created_at").all()
        
        # 转换结果
        result = [
            OrganizationMenuResponse.model_validate(item, from_attributes=True)
            for item in query
        ]
        
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"获取机构菜单关联失败: {str(e)}")

@router.get("/tree", response_model=ResponseModel[List[MenuTreeResponse]], summary="获取机构已经有的菜单列表（树状）【超级管理员、机构管理员】")
async def get_organization_menu_tree(
    organization_id: Optional[UUID] = Query(None, description="机构的ID,机构管理员的时候可以不传，超级管理员需要传"),
    current_user: UserResponse = Depends(get_current_user)
):
    """获取机构的菜单树(机构管理员和超级管理员都可以调)"""
    try:
        # 检查权限
        is_super_admin = current_user.role.identifier == InsetRole.SUPER_ADMIN
        if is_super_admin and not organization_id:
            return send_data(False, None, "超级管理员需要传机构的ID")
        is_org_admin = current_user.role.identifier == InsetRole.ADMIN
        if not is_org_admin and not is_super_admin:
            return send_data(False, None, "权限不足")
        org_id = current_user.organization.id if current_user.role.identifier == InsetRole.ADMIN else organization_id
        # 检查机构是否存在
        organization = await Organizations.filter(id=org_id, is_deleted=False).first()
        if not organization:
            return send_data(False, None, "机构不存在")
        # 获取机构的菜单ID列表
        menu_ids = await OrganizationMenu.filter(
            organization_id=org_id,
            is_deleted=False
        ).values_list("menu_id", flat=True)
        if not menu_ids:
            return send_data(True, [])
            
        # 获取菜单详情
        menus = await Menu.filter(
            id__in=menu_ids, 
            is_deleted=False
        ).order_by("order").all()
        
        # 构建菜单树
        menu_dict = {}
        root_menus = []
        
        # 先将所有菜单放入字典
        for menu in menus:
            menu_dict[menu.id] = MenuTreeResponse.model_validate(menu, from_attributes=True)
            menu_dict[menu.id].children = []
        
        # 构建树形结构
        for menu in menus:
            menu_response = menu_dict[menu.id]
            if menu.parent_id is None or menu.parent_id not in menu_dict:
                # 没有父菜单或父菜单不在权限内，则作为根菜单
                root_menus.append(menu_response)
            else:
                # 将当前菜单添加到父菜单的children中
                menu_dict[menu.parent_id].children.append(menu_response)
        
        # 排序根菜单（按order字段）
        root_menus.sort(key=lambda x: x.order)
        
        return send_data(True, root_menus)
    except Exception as e:
        return send_data(False, None, f"获取机构菜单树失败: {str(e)}")