from typing import Optional, Callable
from app.models.dictionary import Dictionary
from app.models.literature_library import LiteratureLibrary, LiteratureLibraryStatus
from app.services.pubmed_service import batch_search_pubmed, literature_library_get_new_search_queries, literature_library_process_search_query
from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from datetime import datetime
import os
import asyncio
import json
import traceback
import aiohttp
from app.models.organizations import Organizations
from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.models.project_configs import ProjectConfig
from app.api.schemas.project_configs import LiteratureLibraryType, ProjectConfigResponse2, ProjectConfigStatus
from app.api.schemas.project_members import ProjectMemberBase
from app.services.prompts import generate_outline_prompt, OUTLINE_SYSTEM_PROMPT, generate_project_config_prompt
from app.utils.llm_service import stream_llm_and_save
from app.utils.utils import send_data, ResponseModel, stream_file_content_sse
from app.api.routes.project_configs import get_one_project_config
from app.utils.content_manager import ContentManager, Data, ContentStatus
from app.services.llm_token_service import get_generation_result
from app.models.research import Research, ResearchStatus
from app.services.research_service import (
    generate_search_queries,
    process_search_query,
    get_new_search_queries
)
from app.services import prompts
from app.core.config import settings
from app.core.logging import get_logger
# from app.api.routes.model_configs import get_current_user_models
from app.models.user_report_usage import UserReportUsage
from app.api.routes.workflow import create_workflow

# 获取logger实例
logger = get_logger(__name__)

router = APIRouter()

outline_content_manager = ContentManager()
report_content_manager = ContentManager()

async def remove_storage(project_id: str):
    # 直接从数据库获取ProjectConfig模型
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("leader", "user", "model").first()
    # 获取该项目的内容对象
    project_content = outline_content_manager.get_project_content(project_id)
        
    # 如果存在异步任务，尝试取消
    if project_content and project_content.asyncioInstance:
        try:
            project_content.asyncioInstance.cancel()
            # error = f"已取消大纲生成任务"
            print(f"已取消项目 {project_id} 的大纲生成任务")
            # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
        except Exception as e:
            error = f"取消大纲生成任务时出错: {str(e)}"
            # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
            print(error)
        # 更新项目状态为已取消
        config_db.status = ProjectConfigStatus.OUTLINE_CANCELED.value
        config_db.ai_generated_outline = None
        await config_db.save()

# 生成项目大纲接口
@router.post("/{project_id}/generate-outline", response_model=ResponseModel[ProjectConfigResponse2])
async def generate_project_outline(
    project_id: str, current_user: UserResponse = Depends(get_current_user)
):
    # 检查用户报告使用次数
    user_report_usage = await UserReportUsage.filter(user_id=current_user.id, is_deleted=False).first()
    if not user_report_usage:
        # 如果不存在，创建一个新的记录
        return send_data(False, None, "用户报告使用次数记录不存在")
    # 检查是否超过使用限制
    if user_report_usage.max_allowed_count is not None and user_report_usage.used_count >= user_report_usage.max_allowed_count:
        return send_data(False, None, f"您已达到最大使用次数限制({user_report_usage.max_allowed_count}次)")
        
    # 直接从数据库获取ProjectConfig模型
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("leader", "user", "model").first()
    # config_db = ProjectConfigResponse.model_validate(result)
    # print(config_db.user.id, 'config_db')
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    
    if config_db.user.id != current_user.id:
        return send_data(False, None, "无权访问此项目")
    
    if config_db.model is None:
        return send_data(False, None, "请先配置模型")
    firstModel = config_db.model
    apiKey = firstModel.api_key
    apiUrl = firstModel.api_url
    model = firstModel.model_name
    await remove_storage(project_id=project_id)
    # 获取config_response用于生成提示词
    config_response = await get_one_project_config(project_id)
    
    # 生成大纲提示词
    team_members = [
        ProjectMemberBase(
            name=join.member.name,
            title=join.member.title,
            representative_works=join.member.representative_works,
            organization=join.member.organization
        )
        for join in config_response.team_members
    ]
  
    
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"outline_{timestamp}.txt"
    if config_response.name:
        file_name = f"outline_{config_response.name[:10].replace(' ', '_')}_{timestamp}.txt"
    relative_path = f"{project_folder}/{file_name}"
    
    # 获取绝对路径
    abs_file_path = os.path.join(os.getcwd(), relative_path)
    
    # 确保目录和文件存在
    abs_folder_path = os.path.dirname(abs_file_path)
    if not os.path.exists(abs_folder_path):
        os.makedirs(abs_folder_path)
    if not os.path.exists(abs_file_path):
        with open(abs_file_path, 'w', encoding='utf-8') as f:
            f.write('')
    prompt = generate_outline_prompt(
      name=config_response.name or "",
      application_category=config_response.application_category or "",
      leader=config_response.leader,
      team_members=team_members,
      language_style=config_response.language_style or ""
    )
    # 调用LLM生成大纲 - 流式处理并保存到文件
    messages = [
        {"role": "system", "content": OUTLINE_SYSTEM_PROMPT},
        {"role": "user", "content": prompt}
    ]
    async def complete_callback(open_router_id: str): # 存储大纲内容
        config_db.status = ProjectConfigStatus.OUTLINE_GENERATED.value
        config_db.outline_generated_time = datetime.now()
        # 生成大纲的时候要把之前的内容清空
        config_db.manual_modified_outline = None
        config_db.manual_modified_outline_time = None
        config_db.ai_generated_report = None
        config_db.report_generation_time = None
        config_db.manual_modified_report_time = None
        config_db.manual_modified_report = None
        outline_content_manager.save_all_content_to_file(project_id, abs_file_path)
        await create_workflow(
            project_id=project_id,
            content=relative_path,
            name="GENERATED_OUTLINE_FIRST_TIME",
            current_user=current_user
        )
        try:
            temp = await get_generation_result(
                generation_id = open_router_id,
                api_key=apiKey
            )
            config_db.outline_tokens_consumed = temp.data.tokens_completion + temp.data.tokens_prompt
        except Exception as e:
            print(f"{e}")
            config_db.outline_tokens_consumed = 0
        await config_db.save()
        
        # 更新用户使用次数
        # user_report_usage.used_count += 1
        # await user_report_usage.save()
    async def error_callback(error: str):
        # 处理错误
        print(f"生成大纲时发生错误: {error}")
        outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
        config_db.status = ProjectConfigStatus.OUTLINE_FAILED.value
        await config_db.save()
    def callback(content: str):
      outline_content_manager.add_content(project_id, content)
    # 添加一个空字符串，用于触发流式处理
    outline_content_manager.add_content(project_id, "")
    # 创建异步任务并存储
    task = asyncio.create_task(stream_llm_and_save(
        messages=messages, 
        callback=callback,
        complete_callback=complete_callback,
        error_callback=error_callback,
        model=model,
        apiKey=apiKey,
        apiUrl=apiUrl
    ))
    # 将任务实例保存到内容管理器
    outline_content_manager.add_asyncio(project_id, task)
    
    # 更新项目配置 - 使用数据库模型
    config_db.ai_generated_outline = relative_path  # 存储大纲内容
    config_db.status = ProjectConfigStatus.OUTLINE_GENERATING.value
    await config_db.save()
    
    return send_data(True, await get_one_project_config(project_id))

# 实现从研究报告流式生成内容的异步生成器函数
async def stream_research_report_content(
    config_response: ProjectConfigResponse2,
    callback: Optional[Callable[[str], None]] = None,
    complete_callback: Optional[Callable[[str], None]] = None,
    error_callback: Optional[Callable[[str], None]] = None,
    api_key: str = "",
    api_url: str = "",
    model: str = ""
):
    """
    流式生成报告内容
    
    执行真实的搜索-总结-迭代-搜索流程，并流式返回生成结果
    """
    # 直接从数据库获取ProjectConfig模型
    # config_response = await get_one_project_config(project_id)
    # report_content_manager.add_content(config_response.id, '')
    async def wrapper_error_callback(error_msg: str):
        logger.error(error_msg)
        if error_callback:
            await error_callback(error_msg)
    # 检查是否已有大纲
    outline_path = config_response.manual_modified_outline or config_response.ai_generated_outline
    print(outline_path)
    if not outline_path:
      error_msg = "请先生成项目大纲"
      await wrapper_error_callback(error_msg)
      return
      
    # 从文件中读取大纲内容
    try:
        full_path = os.path.join(os.getcwd(), outline_path)
        if not os.path.exists(full_path):
            error_msg = "大纲文件不存在"
            await wrapper_error_callback(error_msg)
            return
            
        with open(full_path, "r", encoding="utf-8") as f:
            outline = f.read()
            
        if not outline:
            error_msg = "大纲文件内容为空"
            await wrapper_error_callback(error_msg)
            return
            # return send_data(False, None, "大纲文件内容为空")
    except Exception as e:
        error_msg = f"读取大纲文件失败: {str(e)}"
        await wrapper_error_callback(error_msg)
        return
        # return send_data(False, None, f"读取大纲文件失败: {str(e)}")
    
    # 生成报告提示词
    team_members = [
        ProjectMemberBase(
            name=join.member.name,
            title=join.member.title,
            representative_works=join.member.representative_works,
            organization=join.member.organization
        )
        for join in config_response.team_members
    ]
    prompt = None
    try:
        # 发送开始生成的事件
        # header_content = f"# {config_db.name}\n\n"
        # yield f"data: {json.dumps({'content': header_content, 'type': 'header'})}\n\n".encode('utf-8')
        
        # status_message = "正在开始研究流程，这可能需要几分钟时间...\n\n"
        # yield f"data: {json.dumps({'content': status_message, 'type': 'status'})}\n\n".encode('utf-8')
        
        # 创建一个研究实例来处理报告生成
        try:
            # 注意：不再使用api_key_id参数
            research = await Research.create(
                query=f"{config_response.name}\n\n大纲：\n{outline}",
                search_queries=[],
                contexts=[],
                status=ResearchStatus.PENDING
            )
            config = await ProjectConfig.filter(id=config_response.id).first()
            config.research = research
            await config.save()
        except Exception as e:
            error_msg = f"创建研究实例失败: {str(e)}"
            await wrapper_error_callback(error_msg)
            return
            # yield f"data: {json.dumps({'content': f'⚠️ {error_msg}', 'type': 'error'})}\n\n".encode('utf-8')
            # yield f"data: {json.dumps({'status': 'error', 'message': error_msg})}\n\n".encode('utf-8')
            
            # 更新报告状态
        
        # 步骤1: 生成初始搜索查询并发送进度
        logger.info(f"研究 {research.id}: 开始生成初始搜索查询")
        # section_header = "## 步骤1: 分析研究主题，生成搜索查询\n\n"
        # yield f"data: {json.dumps({'content': section_header, 'type': 'section'})}\n\n".encode('utf-8')
        print(research, 'research')
        initial_queries = await generate_search_queries(research=research,api_key=api_key,api_url=api_url,model=model)
        
        if not initial_queries:
            error_msg = "⚠️ 无法生成搜索查询，请重试\n\n"
            # yield f"data: {json.dumps({'content': error_msg, 'type': 'error'})}\n\n".encode('utf-8')
            await wrapper_error_callback(error_msg)
            research.status = ResearchStatus.FAILED
            await research.save()
            return
        
        # 更新研究实例并发送查询列表
        research.search_queries = initial_queries
        research.status = ResearchStatus.SEARCHING
        await research.save()
        
        queries_list = '\n'.join([f"- {query}" for query in initial_queries])
        progress_msg = f"已生成以下搜索意向：\n\n{queries_list}\n\n"
        logger.info(progress_msg)
        # yield f"data: {json.dumps({'content': progress_msg, 'type': 'progress'})}\n\n".encode('utf-8')
        
        # 步骤2: 执行搜索和内容分析
        # section2_header = "## 步骤2: 搜索和信息收集\n\n"
        # yield f"data: {json.dumps({'content': section2_header, 'type': 'section'})}\n\n".encode('utf-8')
        
        # 迭代处理每个搜索查询
        iteration = 0
        max_iterations = settings.RESEARCH_ITERATION_LIMIT
        # 添加最大搜索查询次数限制
        max_search_queries = settings.MAX_SEARCH_QUERIES  # 使用配置的最大搜索查询数
        max_contexts = settings.MAX_CONTEXTS  # 使用配置的最大上下文数量
        total_processed_queries = 0
        
        content_so_far = ""
        
        while iteration < max_iterations:
            # 获取当前迭代的查询
            current_queries = research.search_queries
            
            # 检查是否已处理所有查询或达到查询上限
            remaining_queries = current_queries[research.iterations:]
            if not remaining_queries or total_processed_queries >= max_search_queries:
                done_msg = "✅ 所有查询已处理完毕或达到查询上限\n\n"
                logger.info(done_msg)
                # yield f"data: {json.dumps({'content': done_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
                
            # iter_header = f"### 迭代 {iteration+1}/{max_iterations}\n\n"
            # yield f"data: {json.dumps({'content': iter_header, 'type': 'subsection'})}\n\n".encode('utf-8')
            
            # 处理每个查询，但确保不超过最大查询次数
            for query_index, query in enumerate(remaining_queries):
                # 检查是否达到查询上限
                if total_processed_queries >= max_search_queries:
                    limit_msg = f"⚠️ 已达到最大查询次数限制({max_search_queries}个)，停止搜索\n\n"
                    logger.info(limit_msg)
                    # yield f"data: {json.dumps({'content': limit_msg, 'type': 'warning'})}\n\n".encode('utf-8')
                    break
                    
                search_msg = f"正在搜索: \"{query}\"\n\n"
                logger.info(search_msg)
                # yield f"data: {json.dumps({'content': search_msg, 'type': 'status'})}\n\n".encode('utf-8')
                
                # 处理搜索查询
                contexts = await process_search_query(research=research, query=query,api_key=api_key,api_url=api_url,model=model)
                
                # 增加迭代计数和处理查询计数
                research.iterations += 1
                total_processed_queries += 1
                await research.save()
                
                # 报告进度
                if contexts:
                    success_msg = f"✅ 已找到 {len(contexts)} 条相关信息\n\n"
                    logger.info(success_msg)
                    # yield f"data: {json.dumps({'content': success_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                else:
                    warning_msg = "⚠️ 未找到相关信息\n\n"
                    logger.info(warning_msg)
                    # yield f"data: {json.dumps({'content': warning_msg, 'type': 'warning'})}\n\n".encode('utf-8')
            
            # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
            if len(research.contexts) >= max_contexts:  # 使用配置的最大上下文数量
                enough_msg = f"✅ 已收集足够的信息（{len(research.contexts)}条上下文）\n\n"
                logger.info(enough_msg)
                # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
            
            # 分析已收集的信息并获取新的搜索查询
            analyzing_msg = "正在分析已收集的信息...\n\n"
            logger.info(analyzing_msg)
            # yield f"data: {json.dumps({'content': analyzing_msg, 'type': 'status'})}\n\n".encode('utf-8')
            
            research.status = ResearchStatus.ANALYZING
            await research.save()
            
            try:
                new_queries = await get_new_search_queries(research=research,api_key=api_key,api_url=api_url,model=model)
                
                # 如果不需要更多查询，退出循环
                if new_queries is None:
                    complete_msg = "✅ 已收集足够的信息\n\n"
                    logger.info(complete_msg)
                    # yield f"data: {json.dumps({'content': complete_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                    break
                
                # 更新搜索查询并继续
                if new_queries and len(new_queries) > 0:
                    # 限制新增查询数量，避免无限增长
                    new_queries = new_queries[:5]  # 每轮最多添加5个新查询
                    research.search_queries = research.search_queries + new_queries
                    research.status = ResearchStatus.SEARCHING
                    await research.save()
                    
                    new_queries_list = '\n'.join([f"- {query}" for query in new_queries])
                    new_queries_msg = f"已生成新的搜索查询：\n\n{new_queries_list}\n\n"
                    logger.info(new_queries_msg)
                    # yield f"data: {json.dumps({'content': new_queries_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                else:
                    # 无新查询，退出循环
                    done_msg = "✅ 搜索完成，未生成新的查询\n\n"
                    logger.info(done_msg)
                    # yield f"data: {json.dumps({'content': done_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                    break
            except Exception as e:
                error_msg = f"⚠️ 生成新查询时出错: {str(e)}\n\n"
                # yield f"data: {json.dumps({'content': error_msg, 'type': 'error'})}\n\n".encode('utf-8')
                logger.error(f"研究 {research.id}: 生成新查询时出错: {str(e)}")
                # 出错时也要继续，避免整个过程中断
                break
            
            iteration += 1

        # 步骤2-文献库： 判断是否需要文献库查询
        #文献库文本内容
        literature_library_text_content = None
        if config_response.literature_library is not None and config_response.literature_library:
             # 这里添加文献库查询相关代码
            logger.info("## 步骤2: 进行文献库查询\n\n")
            from app.models.user import User
            user_instance = await User.get(id=config_response.user.id)
            # 创建LiteratureLibrary记录
            literature_library = LiteratureLibrary(
                query="",
                search_queries=initial_queries,
                status=LiteratureLibraryStatus.PENDING,
                user=user_instance
            )
            #if config_response.literature_library == LiteratureLibraryType.PUBMED.value:
            literature_library.project_config_id = config_response.id
                # 更新为正在搜索状态
            literature_library.status = LiteratureLibraryStatus.SEARCHING 
            await literature_library.save()
            logger.info(f"创建文献库记录: {literature_library.id}")
            #pubmed文献库查询
            if config_response.literature_library == LiteratureLibraryType.PUBMED.value:
                logger.info(f"创建PubMed文献库记录: {literature_library.id}")
                LiteratureSearchResult = await batch_search_pubmed(
                    initial_queries, 
                    model=model, 
                    api_key=api_key, 
                    api_url=api_url,
                    literature_library=literature_library  # 传递文献库对象
                )
                # 转换LiteratureSearchResult对象列表为指定格式
                formatted_results = []
                for result in LiteratureSearchResult:
                    formatted_results.append({
                        "标题": result.title,
                        "内容": result.abstract
                    })
                literature_library_text_content = formatted_results
            else:
                # 从dictionary表中查询literature_library对应的值
                literature_library_dict = await Dictionary.filter(
                    category_value="参考文献库",
                    value=config_response.literature_library,
                    is_deleted=False
                ).first()
                if literature_library_dict:
                    logger.info(f"使用参考文献库: {literature_library_dict.value}")
                # 其他文献库类型检索
                iteration = 0
                max_iterations = settings.RESEARCH_ITERATION_LIMIT
                # 添加最大搜索查询次数限制
                max_search_queries = settings.MAX_SEARCH_QUERIES  # 使用配置的最大搜索查询数
                max_contexts = settings.MAX_CONTEXTS  # 使用配置的最大上下文数量
                total_processed_queries = 0
                
                # 获取文献库对应的URL
                site_url = None
                if literature_library_dict:
                    site_url = literature_library_dict.remark
        
                
                content_so_far = ""
                
                while iteration < max_iterations:
                    # 获取当前迭代的查询
                    current_queries = literature_library.search_queries
                    
                    # 检查是否已处理所有查询或达到查询上限
                    remaining_queries = current_queries[literature_library.iterations:]
                    if not remaining_queries or total_processed_queries >= max_search_queries:
                        done_msg = "✅ 所有查询已处理完毕或达到查询上限\n\n"
                        logger.info(done_msg)
                        # yield f"data: {json.dumps({'content': done_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                        break
                        
                    # iter_header = f"### 迭代 {iteration+1}/{max_iterations}\n\n"
                    # yield f"data: {json.dumps({'content': iter_header, 'type': 'subsection'})}\n\n".encode('utf-8')
                    
                    # 处理每个查询，但确保不超过最大查询次数
                    for query_index, query in enumerate(remaining_queries):
                        # 检查是否达到查询上限
                        if total_processed_queries >= max_search_queries:
                            limit_msg = f"⚠️ 已达到最大查询次数限制({max_search_queries}个)，停止搜索\n\n"
                            logger.info(limit_msg)
                            # yield f"data: {json.dumps({'content': limit_msg, 'type': 'warning'})}\n\n".encode('utf-8')
                            break
                            
                        search_msg = f"正在搜索: \"{query}\"\n\n"
                        logger.info(search_msg)
                        # yield f"data: {json.dumps({'content': search_msg, 'type': 'status'})}\n\n".encode('utf-8')
                        
                        # 处理搜索查询
                        contexts = await literature_library_process_search_query(literature_library=literature_library, query=query,api_key=api_key,api_url=api_url,model=model,site=site_url)
                        
                        # 增加迭代计数和处理查询计数
                        literature_library.iterations += 1
                        total_processed_queries += 1
                        await literature_library.save()
                        
                        # 报告进度
                        if contexts:
                            success_msg = f"✅ 已找到 {len(contexts)} 条相关信息\n\n"
                            logger.info(success_msg)
                            # yield f"data: {json.dumps({'content': success_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                        else:
                            warning_msg = "⚠️ 未找到相关信息\n\n"
                            logger.info(warning_msg)
                            # yield f"data: {json.dumps({'content': warning_msg, 'type': 'warning'})}\n\n".encode('utf-8')
                    
                    # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
                    if len(literature_library.contexts) >= max_contexts:  # 使用配置的最大上下文数量
                        enough_msg = f"✅ 已收集足够的信息（{len(literature_library.contexts)}条上下文）\n\n"
                        logger.info(enough_msg)
                        # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                        break
                    
                    # 分析已收集的信息并获取新的搜索查询
                    analyzing_msg = "正在分析已收集的信息...\n\n"
                    logger.info(analyzing_msg)
                    # yield f"data: {json.dumps({'content': analyzing_msg, 'type': 'status'})}\n\n".encode('utf-8')
                    
                    literature_library.status = LiteratureLibraryStatus.ANALYZING
                    await literature_library.save()
                    
                    try:
                        new_queries = await literature_library_get_new_search_queries(literature_library,api_key,api_url,model)
                        
                        # 如果不需要更多查询，退出循环
                        if new_queries is None:
                            complete_msg = "✅ 已收集足够的信息\n\n"
                            logger.info(complete_msg)
                            # yield f"data: {json.dumps({'content': complete_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                            break
                        
                        # 更新搜索查询并继续
                        if new_queries and len(new_queries) > 0:
                            # 限制新增查询数量，避免无限增长
                            new_queries = new_queries[:5]  # 每轮最多添加5个新查询
                            literature_library.search_queries = literature_library.search_queries + new_queries
                            literature_library.status = LiteratureLibraryStatus.SEARCHING
                            await literature_library.save()
                            
                            new_queries_list = '\n'.join([f"- {query}" for query in new_queries])
                            new_queries_msg = f"已生成新的搜索查询：\n\n{new_queries_list}\n\n"
                            logger.info(new_queries_msg)
                            # yield f"data: {json.dumps({'content': new_queries_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                        else:
                            # 无新查询，退出循环
                            done_msg = "✅ 搜索完成，未生成新的查询\n\n"
                            logger.info(done_msg)
                            # yield f"data: {json.dumps({'content': done_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                            break
                    except Exception as e:
                        error_msg = f"⚠️ 生成新查询时出错: {str(e)}\n\n"
                        # yield f"data: {json.dumps({'content': error_msg, 'type': 'error'})}\n\n".encode('utf-8')
                        logger.error(f"文献库 {literature_library.id}: 生成新查询时出错: {str(e)}")
                        # 出错时也要继续，避免整个过程中断
                        break
                    
                    iteration += 1
                # 转换contexts为需要的格式：[{标题：title，内容：abstract}, ...]
                formatted_results = []
                for context in literature_library.contexts:
                    if "results" in context and context["results"]:
                        for result in context["results"]:
                            if result.get("is_useful", False):  # 只取有用的结果
                                formatted_results.append({
                                    "标题": result.get("title", ""),
                                    "内容": result.get("abstract", "")
                                })
                literature_library_text_content = formatted_results
        # 步骤3: 生成最终报告
        final_section = "## 步骤3: 生成研究报告\n\n"
        # yield f"data: {json.dumps({'content': final_section, 'type': 'section'})}\n\n".encode('utf-8')
        logger.info(final_section)

        generating_msg = "正在生成最终报告，这可能需要一些时间...\n\n"
        # yield f"data: {json.dumps({'content': generating_msg, 'type': 'status'})}\n\n".encode('utf-8')
        logger.info(generating_msg)

        research.status = ResearchStatus.ANALYZING
        await research.save()
        
        # 打印research.query和research.contexts用于调试
        debug_info = f"DEBUG - Query: {research.query}\n\n"
        logger.info(debug_info)
        # yield f"data: {json.dumps({'content': debug_info, 'type': 'debug'})}\n\n".encode('utf-8')

        # 显示收集的上下文数量
        contexts_count = f"DEBUG - 收集到 {len(research.contexts)} 条上下文\n\n"
        logger.info(contexts_count)
        # yield f"data: {json.dumps({'content': contexts_count, 'type': 'debug'})}\n\n".encode('utf-8')

        # 如果需要显示详细内容，可以添加以下代码（谨慎使用，可能输出很多内容）
        for i, context in enumerate(research.contexts[:3]):  # 只显示前3条，避免输出过多
            context_preview = context[:200] + "..." if len(context) > 200 else context
            context_info = f"上下文 #{i+1}:\n{context_preview}\n\n"
            logger.info(context_info)
            # yield f"data: {json.dumps({'content': context_info, 'type': 'debug'})}\n\n".encode('utf-8')

        # 还可以添加日志记录
        logger.info(f"研究 {research.id}: 查询: {research.query}")
        logger.info(f"研究 {research.id}: 收集到 {len(research.contexts)} 条上下文")
        messages = None
        try:
            config = generate_project_config_prompt(
                    name=config_response.name,
                    # application_category=config_response.application_category or "",
                    leader=config_response.leader,
                    team_members=team_members,
                    word_count_requirement=config_response.word_count_requirement or 0,
                    team_introduction=config_response.team_introduction or "",
                    flag='REPORT',
                    # language_style=config_response.language_style or ""
                )
            print(config, 'config')
            participants = "、".join([
                f"{item.name}{item.title or ''}" + 
                (f"，就职于{item.organization}" if item.organization else "") + 
                (f"，{item.education + '学历'}" if item.education else "") + 
                (f"，代表性成就有{item.representative_works}" if item.representative_works else "")
                for item in team_members
            ])
            print(participants, 'participants')
            main = f"{config_response.leader.name}, institution established date {config_response.leader.founded_date}, related projects: {config_response.leader.related_projects}"
            # 准备生成报告的提示词
            prompt = await prompts.final_report_prompt(
                outline=outline,
                language_style=config_response.language_style or "",
                name=config_response.name,
                application_category=config_response.application_category or "",
                config=config,
                participants=participants,
                main=main,
                literatures=research.literatures,
                contexts=research.contexts,
                literature_library_text_content=literature_library_text_content
            )
            messages = [
                {"role": "system", "content": prompts.SYSTEM_PROMPT_CN},
                {"role": "user", "content": prompt}
            ]
            print(f"最终报告的提示词：\n{prompt}")
        except Exception as e:
            print(f"生成报告提示词失败: {str(e)}")
        
        """
        流式调用LLM，实时保存响应内容到文件，并返回完整响应
        
        Args:
            messages: 聊天消息列表
            model: 模型名称
            callback: 可选的回调函数，每接收到一个chunk就调用一次
            complete_callback: 可选的完成回调函数，生成完成时调用
        
        Returns:
            完整的模型响应文本
        """
        logger.info(f"流式调用LLM并保存到文件，模型: {model}")
        logger.info(f"开始请求API: {model}")
        headers = {
            "Authorization": f"Bearer {api_key}",
            "X-Title": "Hi-IdeaGen",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": messages,
            "stream": True
            # "max_tokens": 1000
        }
        
        # 设置超时
        timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
        logger.debug(f"流式LLM请求，超时设置: {timeout.total}秒，消息数量: {len(messages)}")
        
        try:
            full_response = ""
            async with aiohttp.ClientSession() as session:
                logger.debug(f"向OpenRouter发送流式请求: {api_url}")
                async with session.post(
                    api_url,
                    headers=headers,
                    json=payload,
                    timeout=timeout
                ) as resp:
                    if resp.status != 200:
                        error_text = await resp.text()
                        error_msg = f"OpenRouter API 错误: 状态码 {resp.status}, 错误详情: {error_text}"
                        logger.error(error_msg)
                        if error_callback:
                            await error_callback(error_msg)
                        return f"API请求错误: {error_msg}"
                    
                    # 流式响应并写入文件
                    logger.debug(f"开始接收流式LLM响应并写入文件")
                    chunk_count = 0
                    response_debug = []  # 用于调试的响应样本
                    open_router_id: Optional[str] = None
                    # 使用追加模式写入文件
                    try:
                        async for line in resp.content:
                            try:
                                # print("1")
                                line_str = line.decode('utf-8').strip()
                                if not line_str:
                                    continue
                                # print("2")
                                # 收集一些样本用于调试
                                if len(response_debug) < 3:
                                    response_debug.append(line_str)
                                # print("3")
                                # 跳过 "data: " 前缀
                                if line_str.startswith("data: "):
                                    line_str = line_str[6:]
                                # print("4")
                                # 处理流结束标记
                                if line_str == "[DONE]":
                                    logger.debug("流式响应结束")
                                    break
                                # print("5")
                                # 解析 JSON 数据
                                try:
                                    data = json.loads(line_str)
                                    open_router_id = data.get("id")
                                    print(f"open_router_id {open_router_id}")
                                    # 提取内容增量
                                    if 'choices' in data and len(data['choices']) > 0:
                                        delta = data['choices'][0].get('delta', {})
                                        if 'content' in delta and delta['content']:
                                            chunk = delta['content']
                                            full_response += chunk
                                            # 如果有回调函数，则调用
                                            if callback:
                                                callback(chunk)
                                            
                                            chunk_count += 1
                                except json.JSONDecodeError as je:
                                    if "OPENROUTER PROCESSING" in line_str:
                                        logger.info(f"正在正常思考处理...")
                                    else:
                                        logger.error(f"无法解析 JSON '{line_str[:100]}...': {str(je)}")
                                except Exception as e:
                                    logger.error(f"处理响应数据时出错: {str(e)}, 数据: {line_str[:100]}...")
                            except UnicodeDecodeError as ue:
                                logger.error(f"解码响应时出错: {str(ue)}")
                            except Exception as e:
                                logger.error(f"处理响应行时出错: {str(e)}")
                                
                    except Exception as e:
                        logger.error(f"读取响应流时出错: {str(e)}")
                    
                    logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                    
                    # 如果没有收到任何内容，记录更多的调试信息
                    if not full_response:
                        result = f"没有接收到有效内容。响应样本: {response_debug}"
                        logger.warning(result)
                        if error_callback:
                            await error_callback(result)
                        # 写入一些信息到文件，表明请求成功但没有收到内容
                        return f"请求完成，但未收到有效内容"
                    
                    # 调用完成回调
                    if complete_callback:
                        try:
                            await complete_callback(open_router_id)
                        except Exception as e:
                            logger.error(f"执行完成回调时出错: {str(e)}")
                    # 保存最终内容到研究和报告
                    research.report_content = content_so_far
                    research.status = ResearchStatus.COMPLETED
                    await research.save()
                    
                    return full_response
                        
        except asyncio.TimeoutError as te:
            error_msg = f"OpenRouter API 请求超时 ({timeout.total} 秒)"
            logger.error(error_msg)
            return f"请求超时: {error_msg}"
        except aiohttp.ClientError as ce:
            error_msg = f"HTTP客户端错误: {str(ce)}"
            logger.error(error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"调用OpenRouter时发生错误: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"错误详情:\n{traceback.format_exc()}")
            return error_msg 
        except asyncio.CancelledError:
            logger.warning("请求被取消")
            return "请求被取消"
        
    except Exception as e:
        error_msg = f"报告流式生成错误: {str(e)}"
        logger.error(error_msg)
        await wrapper_error_callback(error_msg)
        # # 发送错误事件
        # error_msg = str(e)
        # yield f"data: {json.dumps({'status': 'error', 'message': f'生成报告时出错: {error_msg}'})}\n\n".encode('utf-8')
        
        # 更新报告状态
        # config_db.status = ProjectConfigStatus.REPORT_FAILED.value
        # await config_db.save()

# 生成项目报告接口
@router.post("/{project_id}/generate-report", response_model=ResponseModel[ProjectConfigResponse2])
async def generate_project_report(
    project_id: str, current_user: UserResponse = Depends(get_current_user)
):
    is_diff_count = False
    organization = None
    # 检查用户报告使用次数
    user_report_usage = await UserReportUsage.filter(user_id=current_user.id, is_deleted=False).first()
    if not user_report_usage:
        # 如果不存在，创建一个新的记录
        return send_data(False, None, "用户报告使用次数记录不存在")
    # 检查是否超过使用限制
    if user_report_usage.max_allowed_count is not None and user_report_usage.used_count >= user_report_usage.max_allowed_count:
        return send_data(False, None, f"您已达到最大使用次数限制({user_report_usage.max_allowed_count}次)")
    # 判断是否具有机构
    # if current_user.organization:
    #     organization = await Organizations.filter(id=current_user.organization.id, is_deleted=False, is_active=True).first()
    #     if not organization:
    #         return send_data(False, None, "机构不存在或未激活")
    #     elif organization.limit_count <= organization.use_count:
    #         return send_data(False, None, "机构的可用次数已经用完。")
    # 直接从数据库获取ProjectConfig模型
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("leader", "user", "model").first()
    # config_db = ProjectConfigResponse2.model_validate(result)
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    # print(config_db, 'config_db')
    if config_db.user.id != current_user.id:
        return send_data(False, None, "无权访问此项目")
    if config_db.model is None:
        return send_data(False, None, "请先配置模型")
   
    firstModel = config_db.model
    apiKey = firstModel.api_key
    apiUrl = firstModel.api_url
    model = firstModel.model_name
    await remove_storage(project_id=project_id)
    # 获取config_response用于生成提示词
    config_response = await get_one_project_config(project_id)
    
    # 检查是否已有大纲
    outline_path = config_db.manual_modified_outline or config_db.ai_generated_outline
    if not outline_path:
      return send_data(False, None, "请先生成项目大纲")
      
    # 从文件中读取大纲内容
    try:
        full_path = os.path.join(os.getcwd(), outline_path)
        if not os.path.exists(full_path):
            return send_data(False, None, f"大纲文件不存在: {outline_path}")
            
        with open(full_path, "r", encoding="utf-8") as f:
            outline = f.read()
            
        if not outline:
            return send_data(False, None, "大纲文件内容为空")
    except Exception as e:
        return send_data(False, None, f"读取大纲文件失败: {str(e)}")
    
    # 生成报告提示词
    # prompt = generate_report_prompt(
    #     name=config_response.name or "",
    #     application_category=config_response.application_category or "",
    #     leader=config_response.leader,
    #     team_members=team_members,
    #     word_count_requirement=config_response.word_count_requirement or 0,
    #     outline=outline
    # )
    #print(prompt)
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"report_{timestamp}.txt"
    if config_response.name:
        file_name = f"report_{config_response.name[:10].replace(' ', '_')}_{timestamp}.txt"
    relative_path = f"{project_folder}/{file_name}"
    
    # 获取绝对路径
    abs_file_path = os.path.join(os.getcwd(), relative_path)
    
    # 确保目录和文件存在
    abs_folder_path = os.path.dirname(abs_file_path)
    if not os.path.exists(abs_folder_path):
        os.makedirs(abs_folder_path)
    if not os.path.exists(abs_file_path):
        with open(abs_file_path, 'w', encoding='utf-8') as f:
            f.write('')
    
    async def complete_callback(open_router_id: str): # 存储大纲内容
        nonlocal is_diff_count
        if not is_diff_count:
            # 更新用户使用次数
            user_report_usage.used_count += 1
            await user_report_usage.save()
            if organization:
                organization.use_count += 1
                await organization.save()
            is_diff_count = True
        # 增加机构的
        config_db.status = ProjectConfigStatus.REPORT_GENERATED.value
        config_db.report_generation_time = datetime.now()  # 确保使用不带时区的日期时间
        config_db.manual_modified_report_time = None
        config_db.manual_modified_report = None
        report_content_manager.save_all_content_to_file(project_id, abs_file_path)
        await create_workflow(
            project_id=project_id,
            content=relative_path,
            name="GENERATED_CONTENT_FIRST_TIME",
            current_user=current_user
        )
        try:
            temp = await get_generation_result(
                generation_id=open_router_id,
                api_key=apiKey
            )
            config_db.report_tokens_consumed = temp.data.tokens_completion + temp.data.tokens_prompt
        except Exception as e:
            print(f"{e}")   
            config_db.report_tokens_consumed = 0
        await config_db.save()
    async def error_callback(error):
        # 处理错误
        print(f"生成报告时发生错误: {error}")
        report_content_manager.add_content(project_id, error, ContentStatus.ERROR)
        config_db.status = ProjectConfigStatus.REPORT_FAILED.value
        await config_db.save()
    def callback(content: str):
      report_content_manager.add_content(project_id, content)
    # 添加一个空字符串，用于触发流式处理
    report_content_manager.add_content(project_id, "")
    # 创建异步任务并存储
    # task = asyncio.create_task(stream_llm_and_save(
    #     messages=messages, 
    #     callback=callback,
    #     complete_callback=complete_callback,
    #     error_callback=error_callback
    # ))
    
    # 创建异步任务
    task = asyncio.create_task(stream_research_report_content(
        config_response=config_response,
        complete_callback=complete_callback,
        callback=callback,
        error_callback=error_callback,
        api_key=apiKey,
        api_url=apiUrl,
        model=model
    ))
    
    # 将任务保存到内容管理器中
    report_content_manager.add_asyncio(project_id, task)
    
    # 更新项目配置
    config_db.ai_generated_report = relative_path
    config_db.status = ProjectConfigStatus.REPORT_GENERATING.value
    await config_db.save()
    
    return send_data(True, await get_one_project_config(project_id))

# 流式返回大纲内容的接口（SSE）
@router.get("/{project_id}/stream-outline")
async def stream_project_outline(project_id: str, current_user: UserResponse = Depends(get_current_user)):
    """
    流式返回项目大纲内容（SSE格式）
    """
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        print("项目配置不存在")
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目配置不存在', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    if config_db.user.id != current_user.id:
        print("无权访问此项目")
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '无权访问此项目', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    # 检查正在生成的内容
    if config_db.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
        print('处于生成状态')
        async def stream_realtime_content():
            # 已读取的内容计数
            read_count = 0
            project_content = outline_content_manager.get_project_content(project_id)
            
            if not project_content:
                print("找不到正在生成的内容")
                yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR})}\n\n"
                
            # 先发送已有的内容
            for chunk in project_content.read_chunks:
                yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                await asyncio.sleep(0.02)
            print('发送已有的内容', config_db.status)
            # 然后持续检查是否有新内容
            while config_db.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
                chunk = outline_content_manager.read_next_chunk(project_id)
                if chunk:
                    print('发送新内容', chunk)
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                    read_count += 1
                else:
                    # 暂时没有新内容，等待一会再检查
                    await asyncio.sleep(0.5)
                    # 重新获取数据库状态，检查是否仍在生成中
                    await config_db.refresh_from_db()
            
            # 获取剩余未读取的内容
            project_content = outline_content_manager.get_project_content(project_id)
            if project_content and project_content.unread_chunks:
                for chunk in project_content.unread_chunks:
                    print('发送剩余未读内容', chunk.content)
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                    read_count += 1
            
            # 流式内容结束后清理资源
            outline_content_manager.clear_project(project_id)
            print(f"已清理项目 {project_id} 的大纲内容数据")
            # 生成已完成，发送完成状态
            yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )
    print('不处于生成状态')
    # 如果不是正在生成状态，则回退到从文件读取
    outline_path = config_db.manual_modified_outline or config_db.ai_generated_outline
    if not outline_path:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目尚未生成大纲', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(outline_path), 
        media_type="text/event-stream"
    )

# 流式返回报告内容的接口（SSE）
@router.get("/{project_id}/stream-report")
async def stream_project_report(project_id: str, current_user: UserResponse = Depends(get_current_user)):
    """
    流式返回项目报告内容（SSE格式）
    """
    logger.info(f"流式返回项目报告内容（SSE格式），项目ID: {project_id}")
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目配置不存在', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    if config_db.user.id != current_user.id:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '无权访问此项目', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 检查正在生成的内容
    if config_db.status == ProjectConfigStatus.REPORT_GENERATING.value:
        async def stream_realtime_content():
            # 已读取的内容计数
            read_count = 0
            project_content = report_content_manager.get_project_content(project_id)
            
            if not project_content:
                yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR})}\n\n"
                return
                
            # 先发送已有的内容
            for chunk in project_content.read_chunks:
                yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                await asyncio.sleep(0.02)
            
            # 然后持续检查是否有新内容
            while config_db.status == ProjectConfigStatus.REPORT_GENERATING.value:
                chunk = report_content_manager.read_next_chunk(project_id)
                if chunk:
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                    read_count += 1
                else:
                    # 暂时没有新内容，等待一会再检查
                    await asyncio.sleep(0.5)
                    # 重新获取数据库状态，检查是否仍在生成中
                    await config_db.refresh_from_db()
            
            # 获取剩余未读取的内容
            project_content = report_content_manager.get_project_content(project_id)
            if project_content and project_content.unread_chunks:
                for chunk in project_content.unread_chunks:
                    print('发送剩余未读内容', chunk)
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                    read_count += 1
            
            report_content_manager.clear_project(project_id)
            # 生成已完成，发送完成状态
            yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )
    
    # 如果不是正在生成状态，则回退到从文件读取
    report_path = config_db.manual_modified_report or config_db.ai_generated_report
    if not report_path:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目生成报告失败', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(report_path), 
        media_type="text/event-stream"
    )

@router.post("/{project_id}/stop-outline", response_model=ResponseModel[bool])
async def stop_outline_generation(
    project_id: str, 
    current_user: UserResponse = Depends(get_current_user)
):
    """终止项目大纲生成"""
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        return send_data(False, False, "项目配置不存在")
    
    if config_db.user.id != current_user.id:
        return send_data(False, False, "无权访问此项目")
    
    # 检查当前状态
    if config_db.status != ProjectConfigStatus.OUTLINE_GENERATING.value:
        return send_data(False, False, "当前没有正在生成大纲")
    
    try:
        # 获取该项目的内容对象
        project_content = outline_content_manager.get_project_content(project_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                error = f"已取消大纲生成任务"
                print(f"已取消项目 {project_id} 的大纲生成任务")
                outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
            except Exception as e:
                error = f"取消大纲生成任务时出错: {str(e)}"
                outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                print(error)
    
        
        # 更新项目状态为已取消
        config_db.status = ProjectConfigStatus.OUTLINE_CANCELED.value
        config_db.ai_generated_outline = None
        await config_db.save()
        
        return send_data(True, True, "已终止大纲生成")
    except Exception as e:
        return send_data(False, False, f"终止大纲生成失败: {str(e)}")

@router.post("/{project_id}/stop-report", response_model=ResponseModel[bool])
async def stop_report_generation(
    project_id: str, 
    current_user: UserResponse = Depends(get_current_user)
):
    """终止项目报告生成"""
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        return send_data(False, False, "项目配置不存在")
    
    if config_db.user.id != current_user.id:
        return send_data(False, False, "无权访问此项目")
    
    # 检查当前状态
    if config_db.status != ProjectConfigStatus.REPORT_GENERATING.value:
        return send_data(False, False, "当前没有正在生成报告")
    
    try:
        # 获取该项目的内容对象
        project_content = report_content_manager.get_project_content(project_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                error = f"已取消报告生成任务"
                report_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                print(f"已取消项目 {project_id} 的报告生成任务")
            except Exception as e:
                error = f"取消报告生成任务时出错: {str(e)}"
                report_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                # 记录错误信息
                print(f"取消报告生成任务时出错: {str(e)}")
        
        # 更新项目状态为已取消
        config_db.status = ProjectConfigStatus.REPORT_CANCELED.value
        config_db.report_generation_time = None
        config_db.ai_generated_report = None
        await config_db.save()
        
        return send_data(True, True, "已终止报告生成")
    except Exception as e:
        return send_data(False, False, f"终止报告生成失败: {str(e)}")
