from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from tortoise.expressions import Q
import aiohttp
import json

from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.models.model_config import ModelConfig
from app.api.schemas.model_config import (
    ModelConfigCreate,
    ModelConfigUpdate,
    ModelConfigResponse,
    ModelConfigList
)
from app.utils.utils import send_data, ResponseModel
from app.services.llm_token_service import test_model_connectivity
from app.core.logging import get_logger
logger = get_logger(__name__)
router = APIRouter()


@router.post("/", response_model=ResponseModel[ModelConfigResponse], status_code=status.HTTP_201_CREATED)
async def create_model_config(
    model_config_in: ModelConfigCreate,
    current_user: UserResponse = Depends(get_current_user)
):
    """创建新的模型配置"""
    try:
        # 检查模型名称是否已存在
        existing_config = await ModelConfig.filter(
            model_name=model_config_in.model_name,
            user_id=current_user.id,
            is_deleted=False
        ).first()
        if existing_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="模型名称已存在"
            )
        
        # 检查是否是第一个模型
        existing_models = await ModelConfig.filter(
            user_id=current_user.id,
            is_deleted=False
        ).count()
        
        # 如果是第一个模型，强制设置为默认
        if existing_models == 0:
            model_config_in.is_active = True
        else:
            # 如果设置为非激活状态，检查是否已有激活的模型配置
            if not model_config_in.is_active:
                active_config = await ModelConfig.filter(
                    user_id=current_user.id,
                    is_active=True,
                    is_deleted=False
                ).first()
                if not active_config:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="必须选择一个做为默认模型！"
                    )
        
        # 检查模型API连通性
        is_connected, error_msg = await test_model_connectivity(
            api_key=model_config_in.api_key,
            api_url=model_config_in.api_url,
            model=model_config_in.model_name
        )
        
        if not is_connected:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        
        # 如果设置为激活状态，将用户其他模型配置设为非激活
        if model_config_in.is_active:
            await ModelConfig.filter(
                user_id=current_user.id, 
                is_deleted=False
            ).update(is_active=False)
        
        # 创建新的模型配置
        model_config = await ModelConfig.create(
            **model_config_in.model_dump(),
            user_id=current_user.id
        )
        
        return send_data(True, ModelConfigResponse.model_validate(model_config)) 
    except HTTPException as e:
        return send_data(False, None, e.detail)
    except Exception as e:
        return send_data(False, None, f"创建模型配置失败: {str(e)}")


# 将 /current/models 路由放在 /{model_config_id} 前面以避免路由冲突
@router.get("/current/models", response_model=ResponseModel[List[ModelConfigResponse]])
async def get_current_user_models(
    current_user: UserResponse = Depends(get_current_user)
):
    """查询当前用户的所有模型配置
    
    默认只返回未删除的模型 (is_deleted=False)
    """
    try:
        # 构建查询条件
        query = Q(user_id=current_user.id, is_deleted=False)
        
        # 执行查询获取所有符合条件的模型，添加排序: is_active=True在前，然后按updated_at倒序
        model_configs = await ModelConfig.filter(query).order_by("-is_active", "-updated_at")
        
        # 转换为响应模型列表
        result = [ModelConfigResponse.model_validate(config) for config in model_configs]
        
        # 使用 send_data 函数包装返回结果
        return send_data(is_success=True, data=result)
    except Exception as e:
        return send_data(False, None, f"获取模型配置列表失败: {str(e)}")


@router.put("/{model_config_id}", response_model=ResponseModel[ModelConfigResponse])
async def update_model_config(
    model_config_id: str,
    model_config_in: ModelConfigUpdate,
    current_user: UserResponse = Depends(get_current_user)
):
    """更新模型配置"""
    try:    
        model_config = await ModelConfig.filter(
            id=model_config_id,
            user_id=current_user.id,
            is_deleted=False
        ).first()
        
        if not model_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型配置不存在"
            )
        
        
        # 获取更新数据
        update_data = model_config_in.model_dump(exclude_unset=True)
          # 如果设置为非激活状态，检查是否已有其他激活的模型配置
        if "is_active" in update_data and not update_data["is_active"]:
            active_config = await ModelConfig.filter(
                user_id=current_user.id,
                is_active=True,
                is_deleted=False
            ).exclude(id=model_config_id).first()
            if not active_config:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="必须选择一个做为默认模型！"
                )
        # 如果更新了模型名称，检查名称是否已存在
        if "model_name" in update_data and update_data["model_name"] != model_config.model_name:
            existing_config = await ModelConfig.filter(
                model_name=update_data["model_name"],
                is_deleted=False
            ).exclude(id=model_config_id).first()
            if existing_config:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="模型名称已存在"
                )
        
        # 检查模型API连通性
        if "api_key" in update_data or "api_url" in update_data or "model_name" in update_data:
            api_key = update_data.get("api_key", model_config.api_key)
            api_url = update_data.get("api_url", model_config.api_url)
            model_name = update_data.get("model_name", model_config.model_name)
            
            is_connected, error_msg = await test_model_connectivity(
                api_key=api_key,
                api_url=api_url,
                model=model_name
            )
            
            if not is_connected:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=error_msg
                )
        
      
        # 如果设置为激活状态，将用户其他模型配置设为非激活
        if update_data.get("is_active", False):
            logger.info(f"update_data: {update_data}")
            await ModelConfig.filter(
                user_id=current_user.id, 
                is_deleted=False
            ).exclude(id=model_config_id).update(is_active=False)
        
        # 更新模型配置
        await model_config.update_from_dict(update_data)
        await model_config.save()
        
        return send_data(True, ModelConfigResponse.model_validate(model_config)) 
    except HTTPException as e:
        return send_data(False, None, e.detail)
    except Exception as e:
        return send_data(False, None, f"更新模型配置失败: {str(e)}")


@router.get("/", response_model=ResponseModel[ModelConfigList])
async def list_model_configs(
    current_user: UserResponse = Depends(get_current_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    model_name: str = None,
    is_active: bool = None
):
    """分页查询模型配置"""
    try:
        # 构建查询条件
        query = Q(user_id=current_user.id, is_deleted=False)
        if model_name:
            query &= Q(model_name__icontains=model_name)
        if is_active is not None:
            query &= Q(is_active=is_active)
        
        # 执行查询，添加排序: is_active=True在前，然后按updated_at倒序
        total = await ModelConfig.filter(query).count()
        model_configs = await ModelConfig.filter(query).order_by("-is_active", "-updated_at").offset(skip).limit(limit)
        
        # 使用统一的响应格式
        result = {
            "total": total,
            "items": [ModelConfigResponse.model_validate(config) for config in model_configs]
        }
        
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"查询模型配置失败: {str(e)}")


@router.get("/{model_config_id}", response_model=ResponseModel[ModelConfigResponse])
async def get_model_config(
    model_config_id: str,
    current_user: UserResponse = Depends(get_current_user)
):
    """获取单个模型配置"""
    try:
        model_config = await ModelConfig.filter(
            id=model_config_id,
            user_id=current_user.id,
            is_deleted=False
        ).first()
        
        if not model_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型配置不存在"
            )
        
        return send_data(True, ModelConfigResponse.model_validate(model_config))
    except HTTPException as e:
        return send_data(False, None, e.detail)
    except Exception as e:
        return send_data(False, None, f"获取模型配置失败: {str(e)}")


@router.delete("/{model_config_id}", response_model=ResponseModel[ModelConfigResponse])
async def delete_model_config(
    model_config_id: str,
    current_user: UserResponse = Depends(get_current_user)
):
    """逻辑删除模型配置"""
    try:
        # 检查是否是最后一个模型
        total_models = await ModelConfig.filter(
            user_id=current_user.id,
            is_deleted=False
        ).count()
        
        if total_models <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除最后一个模型配置"
            )
        
        model_config = await ModelConfig.filter(
            id=model_config_id,
            user_id=current_user.id,
            is_deleted=False
        ).first()
    
        if not model_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型配置不存在"
            )
        
        # 如果删除的是默认模型，将第一个非删除的模型设置为默认
        if model_config.is_active:
            first_model = await ModelConfig.filter(
                user_id=current_user.id,
                is_deleted=False
            ).exclude(id=model_config_id).order_by("created_at").first()
            
            if first_model:
                first_model.is_active = True
                await first_model.save()
        
        # 执行逻辑删除
        model_config.is_deleted = True
        await model_config.save()
        
        return send_data(True, ModelConfigResponse.model_validate(model_config)) 
    except HTTPException as e:
        return send_data(False, None, e.detail)
    except Exception as e:
        return send_data(False, None, f"删除模型配置失败: {str(e)}")

