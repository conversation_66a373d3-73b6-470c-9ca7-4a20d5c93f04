from typing import List
from app.utils.crypto import sm4_decrypt
from fastapi import APIRouter, Depends, HTTPException, status
from uuid import UUID
from datetime import datetime

from app.api.deps import get_current_user
from app.core.security import get_password_hash
from app.models.user import User
from app.models.organizations import Organizations
from app.models.user_report_usage import UserReportUsage
from app.api.schemas.user import UserCreate, UserResponse, UserUpdate
from app.api.schemas.user_report_usage import UserReportUsageResponse
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger
from app.models.role import Role
from app.api.schemas.role import InsetRole, PERMISSION_LEVELS

logger = get_logger(__name__)
router = APIRouter()


@router.get("/me", response_model=ResponseModel[UserResponse])
async def read_users_me(current_user: UserResponse = Depends(get_current_user)):
    """获取当前用户信息"""
    try:
        # 预加载机构关系信息
        user = await User.filter(id=current_user.id).prefetch_related('role', 'organization').first()
        if not user:
            return send_data(False, None, "用户不存在")
        result = UserResponse.model_validate(user, from_attributes=True)
        # 检查用户是否还有可用的报告生成次数
        
        user_report_usage = await UserReportUsage.filter(user_id=user.id, is_deleted=False).first()
        if current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]:
            result.is_could_create_report = user_report_usage.used_count < user_report_usage.max_allowed_count
            organization = await Organizations.filter(id=current_user.organization.id, is_deleted=False, is_active=True).first()
            if not organization:
                return send_data(False, None, "组织不存在")
            result.is_trial = organization.is_trial
        # 构建响应，添加organization_name
        # user_dict = {
        #     "id": user.id,
        #     "username": user.username,
        #     "created_at": user.created_at,
        #     "position": user.position,
        #     "company": user.company,
        #     "achievement": user.achievement,
        #     "realname": user.realname,
        #     "updated_at": user.updated_at,
        #     "role_name": user.role_name,
        #     "organization_id": user.organization_id,
        #     "organization_name": user.organization.name if user.organization else None
        # }
        
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"获取用户信息失败: {str(e)}")


@router.get("/", response_model=ResponseModel[List[UserResponse]])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    current_user: UserResponse = Depends(get_current_user)
):
    organization = current_user.organization.id if current_user.organization else None
    """获取所有用户"""
    try:
        # 预加载organization关系
        users = await User.filter(is_deleted=False, organization=organization).prefetch_related('organization', 'role').order_by("-updated_at").offset(skip).limit(limit)
        result = [UserResponse.model_validate(user) for user in users]
        for user in result:
            user.is_trial = user.organization and user.organization.is_trial

        # 构建响应，添加organization_name
            
        return send_data(True, result)
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return send_data(False, None, f"获取用户列表失败: {str(e)}")


@router.post("/", response_model=ResponseModel[UserResponse], status_code=status.HTTP_201_CREATED)
async def create_user(
    user_in: UserCreate,
    current_user: UserResponse = Depends(get_current_user)
):
    logger.info(f"创建用户: {user_in}")
    """创建新用户"""
    try:
        user = await User.filter(username=user_in.username, is_deleted=False, organization=current_user.organization.id).first()
        if user:
            return send_data(False, None, "用户名已存在")
          # 解密密码
        try:
            decrypted_password = sm4_decrypt(user_in.password)
            if not decrypted_password:
                return send_data(False, None, "密码解密失败")
        except Exception as e:
            return send_data(False, None, f"密码解密失败: {str(e)}")
        role = await Role.get_or_none(id=user_in.role_id,organization=current_user.organization.id)
        if not role:
            return send_data(False, None, "角色不存在")
        # 机构管理员不能创建超级管理员
        if (current_user.role.identifier != InsetRole.SUPER_ADMIN
            and role.identifier == InsetRole.SUPER_ADMIN):
            return send_data(False, None, "角色权限不够")
        if (current_user.role.identifier != InsetRole.ADMIN
            and current_user.role.identifier != InsetRole.SUPER_ADMIN
            and role.identifier == InsetRole.ADMIN):
            return send_data(False, None, "角色权限不够")
        organization = await Organizations.filter(id=current_user.organization.id).first()
        if not organization:
            return send_data(False, None, f"机构不存在")
        user_obj = await User.create(
            username=user_in.username,
            hashed_password=get_password_hash(decrypted_password),
            company=user_in.company,
            achievement=user_in.achievement,
            position=user_in.position,
            realname=user_in.realname,
            role=role,
            organization=organization
        )
        # await user_obj.save()
        return send_data(True, UserResponse.model_validate(user_obj))
    except Exception as e:
        return send_data(False, None, f"创建用户失败: {str(e)}")


@router.put("/{user_id}", response_model=ResponseModel[UserResponse])
async def update_user(
    user_id: UUID, 
    user_in: UserUpdate, 
    current_user: UserResponse = Depends(get_current_user)
):
    """修改用户信息"""
    try:
        user = await User.filter(id=user_id, is_deleted=False).first()
        if not user:
            return send_data(False, None, "用户不存在")
        
        update_data = user_in.model_dump(exclude_unset=True)
        # 如果更新密码，需要先加密
        if "password" in update_data:
               # 解密密码
            try:
                decrypted_password = sm4_decrypt(user_in.password)
                if not decrypted_password:
                    return send_data(False, None, "密码解密失败")
            except Exception as e:
                return send_data(False, None, f"密码解密失败: {str(e)}")
            update_data.pop("password") # 移除原始密码
            update_data["hashed_password"] = get_password_hash(decrypted_password)
            
        # 处理organization_id字段
            
        # 添加更新时间
        update_data["updated_at"] = datetime.now()
        if "role_id" in user_in:
            role = await Role.filter(id=user_in["role_id"]).first()
            if not role:
                return send_data(False, None, "角色不存在")
            # 机构管理员不能创建超级管理员
            if (current_user.role.identifier != InsetRole.SUPER_ADMIN
                and role.identifier == InsetRole.SUPER_ADMIN):
                return send_data(False, None, "角色权限不够")
            if (current_user.role.identifier != InsetRole.ADMIN
                and current_user.role.identifier != InsetRole.SUPER_ADMIN
                and role.identifier == InsetRole.ADMIN):
                return send_data(False, None, "角色权限不够")
        # 更新用户信息
        await user.update_from_dict(update_data)
        
        # 获取更新后的用户信息
        updated_user = await User.filter(id=user_id).prefetch_related("role", "organization").first()
        return send_data(True, UserResponse.model_validate(updated_user, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"更新用户信息失败: {str(e)}")


@router.delete("/{user_id}", response_model=ResponseModel[UserResponse])
async def delete_user(
    user_id: UUID, 
    current_user: UserResponse = Depends(get_current_user)
):
    """删除用户（逻辑删除）"""
    try:
        user = await User.filter(
            id=user_id,
            is_deleted=False
        ).prefetch_related("role", "organization").first()
        if not user:
            return send_data(False, None, "用户不存在")
        current_role = PERMISSION_LEVELS.get(current_user.role.identifier) or 0
        target_role = PERMISSION_LEVELS.get(user.role.identifier) or 0
        if target_role >= current_role:
            return send_data(False, None, "权限不足")
        user.is_deleted = True
        user.deleted_at = datetime.now()
        await user.save()
        # 逻辑删除
        # await User.filter(id=user_id).update(is_deleted=True)
        
        # 获取更新后的用户信息
        # updated_user = await User.get(id=user_id)
        return send_data(True, UserResponse.model_validate(user))
    except Exception as e:
        return send_data(False, None, f"删除用户失败: {str(e)}")
