from fastapi import APIRouter, Depends, Query, HTTPException, status
from typing import List, Optional, Dict, Any, Generic, TypeVar
from tortoise.expressions import Q
from datetime import datetime
from pydantic.generics import GenericModel
from uuid import UUID
import httpx
from fastapi.security import OAuth2PasswordBearer

from app.api.schemas.insight.knowledge_canvas import (
    KnowledgeCanvasResponse,
    KnowledgeCanvasQueryParams,
    KnowledgeCanvasCreate,
    KnowledgeCanvasUpdate
)
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger
from app.api.deps import get_current_user, oauth2_scheme
from app.models.user import User

# 定义一个通用的分页响应模型
T = TypeVar('T')
class PaginationResponse(GenericModel, Generic[T]):
    """通用分页响应模型"""
    items: T
    total: int
    page: int
    page_size: int

router = APIRouter()
logger = get_logger(__name__)

@router.get(
    "/list",
    response_model=ResponseModel[PaginationResponse[List[KnowledgeCanvasResponse]]],
    summary="获取知识画布列表",
    description="获取当前用户的知识画布列表，支持关键词、类型等多条件筛选。"
)
async def get_knowledge_canvas_list(
    params: KnowledgeCanvasQueryParams = Depends(),
    current_user: User = Depends(get_current_user)
):
    """获取知识画布列表"""
    try:
        # 构建查询条件
        query = Q(is_deleted=False) & Q(user=current_user)
        
        if params.keyword:
            # 支持多字段模糊搜索
            query = query & (
                Q(name__icontains=params.keyword) |
                Q(summary__icontains=params.keyword) |
                Q(key_notes__icontains=params.keyword) |
                Q(related_notes__icontains=params.keyword) |
                Q(ai_questions__icontains=params.keyword)
            )
        
        if params.source_type:
            query = query & Q(source_type=params.source_type)
            
        if params.type:
            query = query & Q(type=params.type)
        
        # 计算总数
        total = await KnowledgeCanvas.filter(query).count()
        
        # 分页查询
        offset = (params.page - 1) * params.page_size
        canvas_list = await KnowledgeCanvas.filter(query).prefetch_related('tags').offset(offset).limit(params.page_size).order_by('-created_at')
        
        # 构建响应数据
        result = PaginationResponse(
            items=canvas_list,
            total=total,
            page=params.page,
            page_size=params.page_size
        )
        
        return send_data(True, result)
    except Exception as e:
        logger.error(f"获取知识画布列表失败: {str(e)}")
        return send_data(False, None, f"获取知识画布列表失败: {str(e)}")

@router.get(
    "/{canvas_id}",
    response_model=ResponseModel[KnowledgeCanvasResponse],
    summary="获取知识画布详情",
    description="根据ID获取知识画布的详细信息。"
)
async def get_knowledge_canvas_detail(
    canvas_id: int,
    current_user: User = Depends(get_current_user)
):
    """获取知识画布详情"""
    try:
        canvas = await KnowledgeCanvas.filter(id=canvas_id, is_deleted=False).first()
        if not canvas:
            return send_data(False, None, "知识画布不存在或已删除")
        
        return send_data(True, canvas)
    except Exception as e:
        logger.error(f"获取知识画布详情失败: {str(e)}")
        return send_data(False, None, f"获取知识画布详情失败: {str(e)}")

@router.post(
    "",
    response_model=ResponseModel[KnowledgeCanvasResponse],
    status_code=status.HTTP_201_CREATED,
    summary="创建知识画布",
    description="创建新的知识画布。"
)
async def create_knowledge_canvas(
    basic_ids: List[str],
    current_user: User = Depends(get_current_user)
):
    """创建新的知识画布，接收basic_ids数组"""
    # ... 这里根据basic_ids实现你的业务逻辑 ...
    return send_data(True, basic_ids)

@router.delete(
    "/{canvas_id}",
    response_model=ResponseModel[KnowledgeCanvasResponse],
    summary="软删除知识画布",
    description="根据ID软删除知识画布。"
)
async def soft_delete_knowledge_canvas(
    canvas_id: int,
    current_user: User = Depends(get_current_user)
):
    """软删除知识画布"""
    try:
        canvas = await KnowledgeCanvas.filter(id=canvas_id, is_deleted=False).first()
        if not canvas:
            return send_data(False, None, "知识画布不存在或已删除")
        
        # 标记为已删除
        canvas.is_deleted = True
        canvas.deleted_at = datetime.now()
        await canvas.save()
        
        return send_data(True, canvas)
    except Exception as e:
        logger.error(f"删除知识画布失败: {str(e)}")
        return send_data(False, None, f"删除知识画布失败: {str(e)}")

@router.get(
    "/notes/list",
    response_model=ResponseModel[List[Dict[str, Any]]],
    summary="查询笔记列表",
    description="转发到其他系统，支持关键词、分页、排序等参数。"
)
async def query_notes(
    keyword: Optional[str] = None,
    page: int = Query(1, ge=1, description='页码，必须大于等于1'),
    page_size: int = 10,
    sort_field: str = Query('edit_time', regex='^(edit_time|title)$', description='排序字段，edit_time或title'),
    sort_order: str = Query('desc', regex='^(desc|asc)$', description='排序方式，desc或asc'),
    current_user: User = Depends(get_current_user),
    token: str = Depends(oauth2_scheme)
):
    """查询笔记列表，转发到其他系统，并二次加工返回数据"""
    try:
        url = "http://localhost:8095/api/hiInsight/queryNotes"
        request_data = {
            "keyword": keyword,
            "pageNum": page,
            "pageSize": page_size,
            "sortField": sort_field,
            "sortOrder": sort_order
        }
        headers = {"Authorization": f"Bearer {token}"}
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=request_data, headers=headers)
            if response.status_code != 200:
                logger.error(f"查询笔记列表失败: {response.text}")
                return send_data(False, None, f"查询笔记列表失败: {response.text}")
            result = response.json()
            notes = result.get("data", {}).get("list", [])
            processed = []
            for note in notes:
                raw_time = note.get("editTime") or note.get("createTime")
                date_str = None
                if raw_time:
                    try:
                        date_str = datetime.datetime.fromisoformat(raw_time.replace('Z', '+00:00')).strftime('%Y-%m-%d')
                    except Exception:
                        date_str = raw_time[:10]  # 兜底取前10位
                processed.append({
                    "basicId": note.get("basicId"),
                    "title": note.get("title"),
                    "url": note.get("url"),
                    "update_time": date_str,
                    "tags": [tag.get("tagName") for tag in note.get("tags", [])],
                    "summary": note.get("summary")
                })
            return send_data(True, processed)
    except Exception as e:
        logger.error(f"查询笔记列表失败: {str(e)}")
        return send_data(False, None, f"查询笔记列表失败: {str(e)}")

@router.get(
    "/notes/detail",
    response_model=ResponseModel[List[dict]],
    summary="批量获取笔记详情",
    description="根据basicIds批量获取笔记详情，转发到其他系统并二次加工返回数据。"
)
async def get_note_detail(
    basicIds: List[str] = Query(..., description="笔记ID列表"),
    current_user: User = Depends(get_current_user),
    token: str = Depends(oauth2_scheme)
):
    """根据basicIds批量查询笔记详情，转发到其他系统并二次加工返回数据"""
    try:
        url = "http://localhost:8095/api/hiInsight/getNoteDetail"
        headers = {"Authorization": f"Bearer {token}"}
        params = [("basicIds", bid) for bid in basicIds]
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, params=params)
            if response.status_code != 200:
                logger.error(f"查询笔记详情失败: {response.text}")
                return send_data(False, None, f"查询笔记详情失败: {response.text}")
            result = response.json()
            logger.info(f"查询笔记详情结果: {result}")
            # 从 data.notes 列表里提取笔记数据
            notes_list = result.get("data", {}).get("notes", [])
            processed = []
            for note in notes_list:
                notebasic = note.get("notebasic", {})
                notetext = note.get("notetext", {})
                
                raw_time = notetext.get("editTime") or notetext.get("createTime")
                date_str = None
                if raw_time:
                    try:
                        # 处理可能的时区信息并格式化
                        date_object = datetime.datetime.fromisoformat(raw_time.replace('Z', '+00:00'))
                        date_str = date_object.strftime('%Y-%m-%d')
                    except Exception:
                        # 兜底方案：如果解析失败，直接取前10位
                        date_str = raw_time[:10] if raw_time and len(raw_time) >= 10 else None

                processed.append({
                    "basicId": notebasic.get("basicId"),
                    "update_time": date_str,
                    "content": notetext.get("noteContent")
                })
            return send_data(True, processed)
    except Exception as e:
        logger.error(f"查询笔记详情失败: {str(e)}")
        return send_data(False, None, f"查询笔记详情失败: {str(e)}")


