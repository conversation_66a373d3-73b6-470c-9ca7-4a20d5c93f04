from fastapi import APIRouter
from app.api.routes.insight import knowledge_canvas, knowledge_llm, inspiration

router = APIRouter()

router.include_router(
    knowledge_canvas.router, 
    prefix="/knowledge-canvas", 
    tags=["知识画布"]
)

router.include_router(
    knowledge_llm.router,
    prefix="/knowledge-llm",
    tags=["知识LLM"]
)

router.include_router(
    inspiration.router,
    prefix="/inspiration",
    tags=["灵感"]
) 