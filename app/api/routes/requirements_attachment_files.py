from typing import List, Optional
from app.models.model_config import ModelConfig
from app.services import prompts
from app.services.attachments_service import extract_text_from_file
from app.services.llm_service import call_llm
from app.services.prompts import generate_requirements_analysis_prompt
from fastapi import APIRouter, Depends, status, HTTPException, UploadFile, File
from uuid import UUID
import os
import shutil
from datetime import datetime

from app.api.deps import get_current_user
# from app.models.user import User
from app.api.schemas.user import UserResponse
from app.models.requirements_attachments_files import RequirementsAttachmentFiles
from app.models.project_configs import ProjectConfig
from app.api.schemas.requirements_attachment_files import (
    RequirementsAttachmentResponse,
    RequirementsTypeContent
)
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)
router = APIRouter()


@router.get("", response_model=ResponseModel[List[RequirementsAttachmentResponse]])
async def list_requirements_attachments_files(
    project_id: Optional[UUID] = None,
    current_user: UserResponse = Depends(get_current_user)
):
    """获取申报要求附件列表，可按项目ID筛选"""
    try:
        # 构建查询
        query = RequirementsAttachmentFiles.all()
        
        # 如果指定了项目ID，只查询该项目的附件
        if project_id:
            # 验证当前用户是否有权限查看此项目
            project_config = await ProjectConfig.filter(id=project_id).first()
            if not project_config:
                return send_data(False, None, "项目配置不存在")
            if project_config.user_id != current_user.id:
                return send_data(False, None, "无权查看此项目")
            
            query = query.filter(project_configs_id=project_id)
        else:
            # 如果未指定项目ID，查询当前用户所有可访问的附件
            # 获取用户所有的项目ID
            project_ids = await ProjectConfig.filter(user_id=current_user.id).values_list('id', flat=True)
            if not project_ids:
                return send_data(True, [])
            query = query.filter(project_configs_id__in=project_ids)
        
        # 执行查询
        attachments = await query.all()
        return send_data(True, attachments)
    except Exception as e:
        return send_data(False, None, f"获取申报要求附件列表失败: {str(e)}")



@router.delete("/{attachment_id}", response_model=ResponseModel)
async def delete_requirements_attachment_files(
    attachment_id: UUID,
    current_user: UserResponse = Depends(get_current_user)
):
    """删除申报要求附件"""
    try:
        # 查找附件
        attachment_file = await RequirementsAttachmentFiles.filter(id=attachment_id).first()
        if not attachment_file:
            return send_data(False, None, "申报要求附件不存在")
        
        # 验证当前用户是否有权限操作此附件
        project_config = await ProjectConfig.filter(id=attachment_file.project_configs_id).first()
        if project_config and project_config.user_id != current_user.id:
            return send_data(False, None, "无权操作此附件")
        
        # 删除文件
        file_path = attachment_file.file_path
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已删除文件: {file_path}")
            else:
                logger.warning(f"文件不存在或路径为空: {file_path}")
        except Exception as file_error:
            # 文件删除失败但继续处理，记录警告
            logger.warning(f"删除文件失败: {file_path}, 错误: {str(file_error)}")
        
        # 删除文件记录
        await attachment_file.delete()
        
        return send_data(True, None, "删除成功")
    except Exception as e:
        logger.error(f"删除申报要求附件失败: {str(e)}")
        return send_data(False, None, f"删除申报要求附件失败: {str(e)}") 
    

@router.post("/process", response_model=ResponseModel[RequirementsTypeContent])
async def process_requirements_attachment_files(
    file: UploadFile = File(...),
    project_configs_name: Optional[str] = None,
    current_user: UserResponse = Depends(get_current_user)
):
    """直接上传文件进行解析"""
    try:
        logger.info(f"开始处理上传的申报要求文档")
        
        # 验证文件
        if not file.filename:
            return send_data(False, None, "文件名不能为空")
            
        # 获取文件扩展名
        file_extension = os.path.splitext(file.filename)[1].lower()
        if not file_extension:
            return send_data(False, None, "文件必须包含扩展名")
            
        # 验证文件类型
        allowed_extensions = ['.pdf', '.doc', '.docx', '.txt']
        if file_extension not in allowed_extensions:
            return send_data(False, None, f"不支持的文件类型: {file_extension}")
            
        # 重置文件指针
        await file.seek(0)
        
        # 准备保存文件的目录
        date_str = datetime.now().strftime("%Y%m%d")
        user_dir = os.path.join(settings.ATTACHMENTS_DIR, str(current_user.id), date_str)
        if not os.path.exists(user_dir):
            os.makedirs(user_dir, exist_ok=True)
        
        # 生成文件名和保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        original_filename = file.filename
        safe_filename = f"{timestamp}_{original_filename}"
        
        # 用于保存的实际文件路径
        file_save_path = os.path.join(user_dir, safe_filename)
        
        # 用于数据库的标准化路径，使用正斜杠
        db_file_path = f"{settings.ATTACHMENTS_DIR}/{current_user.id}/{date_str}/{safe_filename}"
        
        # 保存文件
        try:
            with open(file_save_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            logger.info(f"文件保存成功: {file_save_path}")
        except Exception as save_error:
            logger.error(f"保存文件失败: {str(save_error)}")
            return send_data(False, None, f"保存文件失败: {str(save_error)}")
            
        # 读取文件内容并计算字数
        try:
            # 使用extract_text_from_file函数处理文件
            file_content = await extract_text_from_file(file_save_path)
            if not file_content:
                raise Exception("无法提取文件内容")
            
            word_count = len(file_content)
            
            if word_count > settings.MAX_FILE_WORD_COUNT:
                # 删除已保存的文件
                if os.path.exists(file_save_path):
                    os.remove(file_save_path)
                return send_data(False, None, f"文档字数超过限制（{settings.MAX_FILE_WORD_COUNT}字）")
                
        except Exception as read_error:
            logger.error(f"读取文件内容失败: {str(read_error)}")
            # 删除已保存的文件
            if os.path.exists(file_save_path):
                os.remove(file_save_path)
            return send_data(False, None, f"读取文件内容失败: {str(read_error)}")
            
        # 创建文件记录
        try:
            file_record = await RequirementsAttachmentFiles.create(
                file_path=db_file_path,
                file_name=original_filename,
                file_content=file_content,  # 保存文件内容
                word_count=word_count  # 保存文件字数
            )
            logger.info(f"数据库记录创建成功")
            
            # 调用LLM服务处理文档
            model_config = await ModelConfig.filter(user_id=current_user.id, is_deleted=False, is_active=True).first()
            if not model_config:
                logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
                return send_data(False, None, "用户没有配置LLM模型")
        
            # 获取模型配置
            api_key = model_config.api_key
            api_url = model_config.api_url
            model = model_config.model_name
            
            logger.info(f"使用模型配置: model={model}, api_url={api_url} ,api_key={api_key}")
            
            # 使用prompts.py中的提示词生成函数
            prompt = generate_requirements_analysis_prompt(content=file_content, project_configs_name=project_configs_name)
            
            # 准备消息
            messages = [
                {"role": "system", "content": prompts.REQUIREMENTS_ATTACHMENT_ANALYSIS_SYSTEM},
                {"role": "user", "content": prompt}
            ]
            
            # 调用LLM
            llm_response = await call_llm(
                messages, 
                flag="附件内容分析", 
                model=model,
                apiKey=api_key, 
                apiUrl=api_url
            )
        
            if llm_response:
                # 更新文件记录的分析结果
                file_record.analysis_result = llm_response
                await file_record.save()
            
            logger.info(f"成功处理文档，字数: {word_count}")
            return send_data(True, file_record, "处理成功")
            
        except Exception as db_error:
            logger.error(f"处理文件失败: {str(db_error)}")
            # 删除已保存的文件
            if os.path.exists(file_save_path):
                os.remove(file_save_path)
            return send_data(False, None, f"处理文件失败: {str(db_error)}")
            
    except Exception as e:
        logger.error(f"处理文档失败: {str(e)}")
        return send_data(False, None, f"处理失败: {str(e)}")


    