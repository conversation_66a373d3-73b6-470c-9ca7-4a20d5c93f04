from typing import List
from uuid import UUID
from fastapi import APIRouter, HTTPException, Depends, status

from app.api.schemas.user_report_usage import (
    UserReportUsageCreate,
    UserReportUsageResponse,
)
from app.models.user_report_usage import UserReportUsage
from app.models.user import User
from app.utils.utils import send_data, ResponseModel
from app.api.schemas.user import UserResponse
from app.api.schemas.role import InsetRole


router = APIRouter(
    prefix="/user-report-usages",
    tags=["用户报告使用次数"],
    responses={404: {"description": "Not found"}},
)


@router.post("/", response_model=ResponseModel[UserReportUsageResponse])
async def create_user_report_usage(
    user_report_usage: UserReportUsageCreate
):
    """创建和修改用户报告最大可用生成次数"""
    # 验证用户存在
    if user_report_usage.user_id:
      user = await User.filter(id=user_report_usage.user_id, is_deleted=False).first()
      if not user:
        return send_data(
            False,
            None,
            "用户不存在"
        )
    user_result = await User.filter(id=user_report_usage.user_id, is_deleted=False).prefetch_related("role", "organization").first()
    user = UserResponse.model_validate(user_result)
    # 检查是否已有记录
    existing = await UserReportUsage.get_or_none(user_id_id=user_report_usage.user_id, is_deleted=False)
    if existing:
      # 检查用户角色并设置适当的值
      if user.role.identifier == InsetRole.ADMIN:
        # 管理员用户设置为null (确保字段能接受null值)
        existing.max_allowed_count = None
      else:
        # 普通用户设置为指定值
        existing.max_allowed_count = user_report_usage.max_allowed_count
      # existing.used_count = 10
      # 确保更改保存到数据库
      await existing.save()
      
      # 将关联对象转换为字典，确保user_id是字符串格式
      existing_data = {
          "id": str(existing.id),
          "user_id": str(existing.user_id_id),
          "used_count": existing.used_count,
          "max_allowed_count": existing.max_allowed_count,
          "created_at": existing.created_at,
          "updated_at": existing.updated_at,
          "is_deleted": existing.is_deleted
      }
      
      result = UserReportUsageResponse.model_validate(existing_data)
      return send_data(
          True,
          result,
          ""
      )
    else: 
    # 创建新记录
      new_usage = await UserReportUsage.create(
        user_id_id=user_report_usage.user_id,
        used_count=0,
        max_allowed_count= None if user.role.identifier == InsetRole.ADMIN else user_report_usage.max_allowed_count
      )
      
      # 将关联对象转换为字典，确保user_id是字符串格式
      new_usage_data = {
          "id": str(new_usage.id),
          "user_id": str(new_usage.user_id_id),
          "used_count": new_usage.used_count,
          "max_allowed_count": new_usage.max_allowed_count,
          "created_at": new_usage.created_at,
          "updated_at": new_usage.updated_at,
          "is_deleted": new_usage.is_deleted
      }
      
      result = UserReportUsageResponse.model_validate(new_usage_data)
      return send_data(
          True,
          result,
          ""
      )


@router.get("/by-user/{user_id}", response_model=ResponseModel[UserReportUsageResponse])
async def get_user_report_usage(
    user_id: UUID
):
    """获取用户报告使用次数详情"""
    # 验证用户存在
    user = await User.filter(id=user_id, is_deleted=False).first()
    if not user:
      return send_data(
        False,
        None,
        "用户不存在"
      )
    user_report_usage = await UserReportUsage.filter(user_id=user_id, is_deleted=False).first()
    if not user_report_usage:
        return send_data(
          False,
          None,
          "用户报告使用次数记录不存在"
        )
    
    # 将关联对象转换为字典，确保user_id是字符串格式
    user_report_data = {
        "id": str(user_report_usage.id),
        "user_id": str(user_report_usage.user_id_id),  # 使用user_id_id字段获取实际ID
        "used_count": user_report_usage.used_count,
        "max_allowed_count": user_report_usage.max_allowed_count,
        "created_at": user_report_usage.created_at,
        "updated_at": user_report_usage.updated_at,
        "is_deleted": user_report_usage.is_deleted
    }
    
    result = UserReportUsageResponse.model_validate(user_report_data)
    return send_data(
        True,
        result,
        ""
    )

# @router.patch("/{user_id}", response_model=UserReportUsageDetail)
# async def update_user_report_usage(
#     user_id: UUID,
#     user_report_usage: UserReportUsageUpdate
# ):
#     """更新用户报告使用次数"""
#     # 查找记录
#     db_user_report_usage = await UserReportUsage.filter(user_id=user_id, is_deleted=False).first()
#     if not db_user_report_usage:
#         return send_data(
#           False,
#           None,
#           "用户报告使用次数记录不存在"
#         )
    
#     # 更新记录
#     update_data = user_report_usage.model_dump(exclude_unset=True)
#     db_user_report_usage.max_allowed_count = update_data.get("max_allowed_count", db_user_report_usage.max_allowed_count)
#     await db_user_report_usage.save()
    
#     result = await UserReportUsageDetail.model_validate(db_user_report_usage)
#     return send_data(
#         True,
#         result,
#         ""
#     ) 