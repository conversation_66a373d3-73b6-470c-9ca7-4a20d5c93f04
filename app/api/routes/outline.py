from typing import Optional
from fastapi import APIRouter, Body
from pydantic import BaseModel
from app.utils.utils import send_data, ResponseModel
from app.services.llm_service import call_llm
from app.api.routes.project_configs import get_one_project_config
from app.core.logging import get_logger
from app.services.prompts import EXPAND_PROMPT_SYSTEM, CONDENSE_PROMPT_SYSTEM, CONTINUE_PROMPT_SYSTEM, POLISH_PROMPT_SYSTEM, EXPAND_PROMPT_USER, POLISH_PROMPT_USER, CONTINUE_PROMPT_USER, CONDENSE_PROMPT_USER
# from app.core.config import settings

logger = get_logger(__name__)

router = APIRouter()

class TextRequest(BaseModel):
    """文本处理请求模型"""
    content: str
    project_id: str
    context: Optional[str] = None
    options: Optional[dict] = None

class TextResponse(BaseModel):
    """文本处理响应模型"""
    original: str
    processed: str

@router.post("/expand", response_model=ResponseModel[TextResponse])
async def expand_text(
    request: TextRequest = Body(...)
):
    """扩写文本内容，使其更加详细充实"""
    try:
        # 获取用户模型配置
        model_config = await get_one_project_config(project_id=request.project_id)
        if model_config is None or model_config.model is None:
            return send_data(False, None, "请先配置模型")
          
        firstModel = model_config.model
        api_key = firstModel.api_key
        api_url = firstModel.api_url
        model = firstModel.model_name
        
        # 准备提示词
        system_prompt = EXPAND_PROMPT_SYSTEM
        user_prompt = EXPAND_PROMPT_USER.format(
            content = request.content,
            context = request.context,
            applicationStyle = "国家自然科学基金(NSFC)"
        )
        
        # 调用LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = await call_llm(
            messages=messages,
            model=model,
            apiKey=api_key,
            apiUrl=api_url
        )
        
        # 返回处理结果
        return send_data(True, TextResponse(
            original=request.content,
            processed=response
        ))
    except Exception as e:
        logger.error(f"扩写文本时出错: {str(e)}")
        return send_data(False, None, f"扩写文本时出错: {str(e)}")

@router.post("/condense", response_model=ResponseModel[TextResponse])
async def condense_text(
    request: TextRequest = Body(...)
):
    """缩写文本内容，保留核心信息"""
    try:
        # 获取用户模型配置
        model_config = await get_one_project_config(project_id=request.project_id)
        if model_config is None or model_config.model is None:
            return send_data(False, None, "请先配置模型")
            
        firstModel = model_config.model
        api_key = firstModel.api_key
        api_url = firstModel.api_url
        model = firstModel.model_name
        
        # 准备提示词
        system_prompt = CONDENSE_PROMPT_SYSTEM
        user_prompt = CONDENSE_PROMPT_USER.format(
            content = request.content,
            context = request.context,
            applicationStyle = "国家自然科学基金(NSFC)"
        )
        
        # 调用LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = await call_llm(
            messages=messages,
            model=model,
            apiKey=api_key,
            apiUrl=api_url
        )
        
        # 返回处理结果
        return send_data(True, TextResponse(
            original=request.content,
            processed=response
        ))
    except Exception as e:
        logger.error(f"缩写文本时出错: {str(e)}")
        return send_data(False, None, f"缩写文本时出错: {str(e)}")

@router.post("/continue", response_model=ResponseModel[TextResponse])
async def continue_text(
    request: TextRequest = Body(...)
):
    """续写文本内容，生成后续内容"""
    try:
        # 获取用户模型配置
        model_config = await get_one_project_config(project_id=request.project_id)
        if model_config is None or model_config.model is None:
            return send_data(False, None, "请先配置模型")
            
        firstModel = model_config.model
        api_key = firstModel.api_key
        api_url = firstModel.api_url
        model = firstModel.model_name
        
        # length_option = request.options.get("length", len(request.content) * 2) if request.options else len(request.content) * 2
        
        # 准备提示词
        system_prompt = CONTINUE_PROMPT_SYSTEM
        user_prompt = CONTINUE_PROMPT_USER.format(
            content = request.content,
            context = request.context,
            applicationStyle = "国家自然科学基金(NSFC)"
        )
        
        # 调用LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = await call_llm(
            messages=messages,
            model=model,
            apiKey=api_key,
            apiUrl=api_url
        )
        
        # 返回完整内容（原文+续写内容）
        return send_data(True, TextResponse(
            original=request.content,
            processed=f"{request.content}\n\n{response}"
        ))
    except Exception as e:
        logger.error(f"续写文本时出错: {str(e)}")
        return send_data(False, None, f"续写文本时出错: {str(e)}")

@router.post("/polish", response_model=ResponseModel[TextResponse])
async def polish_text(
    request: TextRequest = Body(...)
):
    print('polish')
    """润色文本内容，提升表达质量"""
    try:
        # 获取用户模型配置
        model_config = await get_one_project_config(project_id=request.project_id)
        if model_config is None or model_config.model is None:
            return send_data(False, None, "请先配置模型")
            
        firstModel = model_config.model
        api_key = firstModel.api_key
        api_url = firstModel.api_url
        model = firstModel.model_name
        
        # 获取可选参数
        # style = request.options.get("style", "标准") if request.options else "标准"
        
        # 准备提示词
        system_prompt = POLISH_PROMPT_SYSTEM
        user_prompt = POLISH_PROMPT_USER.format(
            content = request.content,
            context = request.context,
            applicationStyle = "国家自然科学基金(NSFC)"
        )
        
        # 调用LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = await call_llm(
            messages=messages,
            model=model,
            apiKey=api_key,
            apiUrl=api_url
        )
        
        # 返回处理结果
        return send_data(True, TextResponse(
            original=request.content,
            processed=response
        ))
    except Exception as e:
        logger.error(f"润色文本时出错: {str(e)}")
        return send_data(False, None, f"润色文本时出错: {str(e)}")