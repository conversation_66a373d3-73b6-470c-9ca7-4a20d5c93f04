from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Request, Header
from fastapi.security import OAuth2PasswordRequestForm
from typing import Optional
import json
from app.core.config import settings
from app.core.security import create_access_token, verify_password
from app.models.user import User
from app.api.schemas.user import Token
from app.utils.utils import send_data, ResponseModel
from app.utils.crypto import sm4_decrypt
from app.core.logging import get_logger
from app.models.user_report_usage import UserReportUsage
from app.models.organizations import Organizations
from app.api.schemas.user import UserResponse
from app.api.schemas.role import InsetRole
router = APIRouter()
# 获取logger实例
logger = get_logger(__name__)

@router.post("/login_old", response_model=ResponseModel[Token])
async def login(
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """用户登录"""
    try:
        logger.info(f"用户登录: {form_data.username}")
        
        user = await User.filter(username=form_data.username, is_deleted=False).prefetch_related('role', 'organization').first()
        if not user or not verify_password(form_data.password, user.hashed_password):
            return send_data(False, None, "用户名或密码错误")
        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=json.dumps({
                "username": user.username
            }), expires_delta=access_token_expires
        )
        token_data = {"access_token": access_token}
        return send_data(True, Token.model_validate(token_data))
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        return send_data(False, None, f"登录失败: {str(e)}")


@router.post("/login", response_model=ResponseModel[Token])
async def encrypted_login(
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """加密登录接口
    
    前端使用SM4加密密码，后端解密后验证
    使用与标准登录相同的OAuth2PasswordRequestForm接收参数
    """
    try:
        logger.info(f"加密登录: {form_data.username}")
        
        # 检查用户是否存在
        user = await User.filter(username=form_data.username, is_deleted=False).prefetch_related('role', 'organization').first()
        if not user:
            return send_data(False, None, "用户名或密码错误")

        # 解密密码
        try:
            decrypted_password = sm4_decrypt(form_data.password)
            if not decrypted_password:
                return send_data(False, None, "密码解密失败")
        except Exception as e:
            return send_data(False, None, f"密码解密失败: {str(e)}")
        
        # 验证密码
        if not verify_password(decrypted_password, user.hashed_password):
            return send_data(False, None, "用户名或密码错误")
        
        
        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=json.dumps({
                "username": user.username
            }), expires_delta=access_token_expires
        )
        
        token_data = {
            "access_token": access_token
        }
        return send_data(True, Token.model_validate(token_data))
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        return send_data(False, None, f"登录失败: {str(e)}") 