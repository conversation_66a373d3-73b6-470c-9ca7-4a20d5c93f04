from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from tortoise.exceptions import DoesNotExist

from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.models.project_configs import ProjectConfig
from app.models.model_config import ModelConfig
from app.models.project_model_config import ProjectModelConfig
from app.api.schemas.project_model_config import (
    ProjectModelConfigCreate,
    ProjectModelConfigResponse,
)
from app.utils.utils import send_data, ResponseModel

router = APIRouter()

@router.post("/", response_model=ResponseModel[ProjectModelConfigResponse], status_code=status.HTTP_201_CREATED)
async def create_or_update_project_model_config(
    config_in: ProjectModelConfigCreate,
    current_user: UserResponse = Depends(get_current_user)
):
    """创建或更新项目模型配置关联
    
    如果指定的project_config_id已存在关联记录，则更新为新的model_config_id
    如果不存在，则创建新的关联记录
    """
    try:
        # 验证project_config_id是否存在
        project_config = await ProjectConfig.filter(
            id=config_in.project_config_id,
            user_id=current_user.id,
            is_deleted=0
        ).first()
        
        if not project_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目配置不存在或无权限访问"
            )
        
        # 验证model_config_id是否存在
        model_config = await ModelConfig.filter(
            id=config_in.model_config_id,
            user_id=current_user.id,
            is_deleted=False
        ).first()
        
        if not model_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型配置不存在或无权限访问"
            )
        
        # 根据project_config_id查询是否已存在关联记录
        existing_config = await ProjectModelConfig.filter(
            project_config_id=config_in.project_config_id
        ).first()
        
        if existing_config:
            # 更新现有记录
            existing_config.model_config_id = config_in.model_config_id
            await existing_config.save()
            return send_data(
                True, 
                ProjectModelConfigResponse(
                    id=existing_config.id,
                    project_config_id=existing_config.project_config_id,
                    model_config_id=existing_config.model_config_id
                ),
                "更新项目模型配置关联成功"
            )
        else:
            # 创建新记录
            new_config = await ProjectModelConfig.create(
                project_config_id=config_in.project_config_id,
                model_config_id=config_in.model_config_id
            )
            
            return send_data(
                True, 
                ProjectModelConfigResponse(
                    id=new_config.id,
                    project_config_id=new_config.project_config_id,
                    model_config_id=new_config.model_config_id
                ),
                "创建项目模型配置关联成功"
            )
    except HTTPException as e:
        return send_data(False, None, e.detail)
    except Exception as e:
        return send_data(False, None, f"操作失败: {str(e)}")


