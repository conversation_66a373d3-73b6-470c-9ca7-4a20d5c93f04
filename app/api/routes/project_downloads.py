from asyncio.log import logger
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import FileResponse
from starlette.background import BackgroundTask
from datetime import datetime, timezone
import os
import tempfile
from pathlib import Path
from docx import Document
import markdown
from bs4 import BeautifulSoup
import io
import docx
from docx.shared import RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
# from app.models.user import User
from app.models.project_configs import ProjectConfig
from app.api.schemas.project_configs import ProjectConfigStatus
# from app.api.schemas.role import InsetRole
from app.utils.utils import send_data, ResponseModel

router = APIRouter()

# 下载项目报告的接口
@router.get("/{project_id}/download-report")
async def download_project_report(project_id: str, current_user: UserResponse = Depends(get_current_user)):
    """
    下载项目报告文件
    
    Args:
        project_id: 项目ID
        current_user: 当前用户
        
    Returns:
        FileResponse: 可下载的报告文件
    """
    # 检查用户是否为试用用户
    if current_user.is_trial:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="下载功能仅对付费用户开放，升级账户即可使用此功能"
        )
    
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目配置不存在"
        )
    
    if config_db.user.id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此项目"
        )
    
    # 检查报告状态和文件是否存在
    if config_db.status != ProjectConfigStatus.REPORT_GENERATED.value:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="报告尚未生成完成或生成失败"
        )
    
    # 获取报告文件路径
    report_path = config_db.ai_generated_report
    if not report_path:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="报告文件路径不存在"
        )
    
    # 处理文件路径
    if os.path.isabs(report_path):
        abs_file_path = report_path
    else:
        abs_file_path = os.path.join(os.getcwd(), report_path)
    
    # 检查文件是否存在
    if not os.path.exists(abs_file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"报告文件不存在: {report_path}"
        )
    
    # 生成下载文件名
    file_name = os.path.basename(abs_file_path)
    if config_db.name:
        # 使用项目名_yyyy-mm-dd的形式作为文件名
        date_str = datetime.now().strftime("%Y-%m-%d")
        download_filename = f"{config_db.name.replace(' ', '_')}_{date_str}.txt"
    else:
        download_filename = file_name
    
    # 返回文件下载响应
    return FileResponse(
        path=abs_file_path,
        filename=download_filename,
        media_type="application/octet-stream"
    )

# 下载项目大纲的接口
@router.get("/{project_id}/download-outline")
async def download_project_outline(project_id: str, current_user: UserResponse = Depends(get_current_user)):
    """
    下载项目大纲文件
    
    Args:
        project_id: 项目ID
        current_user: 当前用户
        
    Returns:
        FileResponse: 可下载的大纲文件
    """
    # 检查用户是否为试用用户
    if current_user.is_trial:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="下载功能仅对付费用户开放，升级账户即可使用此功能"
        )
        
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目配置不存在"
        )
    
    if config_db.user.id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此项目"
        )
    
    # 检查大纲状态和文件是否存在
    if config_db.status not in [ProjectConfigStatus.OUTLINE_GENERATED.value, 
                               ProjectConfigStatus.REPORT_GENERATING.value,
                               ProjectConfigStatus.REPORT_GENERATED.value]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="大纲尚未生成完成或生成失败"
        )
    
    # 获取大纲文件路径（优先使用人工修改的大纲）
    outline_path = config_db.manual_modified_outline or config_db.ai_generated_outline
    if not outline_path:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="大纲文件路径不存在"
        )
    
    # 处理文件路径
    if os.path.isabs(outline_path):
        abs_file_path = outline_path
    else:
        abs_file_path = os.path.join(os.getcwd(), outline_path)
    
    # 检查文件是否存在
    if not os.path.exists(abs_file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"大纲文件不存在: {outline_path}"
        )
    
    # 生成下载文件名
    file_name = os.path.basename(abs_file_path)
    if config_db.name:
        # 使用项目名_yyyy-mm-dd的形式作为文件名
        date_str = datetime.now().strftime("%Y-%m-%d")
        download_filename = f"outline_{config_db.name.replace(' ', '_')}_{date_str}.txt"
    else:
        download_filename = file_name
    
    # 返回文件下载响应
    return FileResponse(
        path=abs_file_path,
        filename=download_filename,
        media_type="application/octet-stream"
    )

# 清理临时文件
def cleanup_files(file_paths):
    """
    清理临时文件
    
    Args:
        file_paths: 文件路径列表
    """
    for path in file_paths:
        try:
            if path and os.path.exists(path):
                os.unlink(path)
        except:
            pass

# 处理Word文档格式化的函数
def format_docx_file(doc):
    """
    统一处理Word文档格式化
    
    Args:
        doc: python-docx的Document对象
        
    Returns:
        doc: 格式化后的Document对象
    """
    # 处理所有段落
    for paragraph in doc.paragraphs:
        # 判断是否为标题段落
        is_heading = paragraph.style.name.startswith('Heading')
        
        if is_heading:
            # 标题段落保持原样式，但更改字体为宋体
            for run in paragraph.runs:
                # 安全地设置字体
                try:
                    # 直接设置字体名称（中英文）
                    run.font.name = '宋体'
                    # 尝试使用更安全的方式设置东亚字体
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    # 设置西文字体为Times New Roman
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                    run.font.bold = True  # 确保标题是粗体
                    # 设置字体颜色为黑色
                    run.font.color.rgb = RGBColor(0, 0, 0)
                except Exception as e:
                    logger.warning(f"设置标题字体时出错: {str(e)}")
        else:
            # 非标题段落设置为正文样式
            paragraph.style = doc.styles['Normal']
            
            # 检查每个run，保留原始的加粗样式
            for run in paragraph.runs:
                # 保存原始加粗状态
                original_bold = run.bold
                
                # 直接设置字体名称（中英文）
                run.font.name = '宋体'
                # 同时设置东亚语言字体（确保中文显示为宋体）
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                # 设置西文字体为Times New Roman
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                
                # 恢复原始加粗状态，而不是强制设为False
                run.font.bold = original_bold
            
            # 设置段落格式：段落间距、行距等
            try:
                if is_heading:
                    # 标题段落设置
                    paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 段后间距12磅
                    paragraph.paragraph_format.space_before = docx.shared.Pt(6)  # 段前间距6磅
                else:
                    # 正文段落设置
                    paragraph.paragraph_format.space_after = docx.shared.Pt(10)  # 段后间距10磅
                    # 使用数值设置行距为1.5倍
                    paragraph.paragraph_format.line_spacing = 1.5
                    paragraph.paragraph_format.first_line_indent = docx.shared.Pt(21)  # 首行缩进两个中文字符
            except Exception as e:
                logger.warning(f"设置段落格式时出错: {str(e)}")
        
        # 强制段落之间有换行
        if paragraph.text.strip() and not is_heading:
            try:
                # 确保段落之间有分隔，防止段落粘连
                paragraph.paragraph_format.keep_with_next = False
                # 如果段落包含参考文献标记 [数字]，可能需要特殊处理
                if any(f"[{i}]" in paragraph.text for i in range(1, 100)):
                    paragraph.paragraph_format.space_after = docx.shared.Pt(6)  
            except Exception as e:
                logger.warning(f"设置段落换行属性时出错: {str(e)}")
    
    return doc

# 通用的Markdown转Word处理函数
def convert_markdown_to_docx(markdown_file_path, output_filename):
    """
    将Markdown文件转换为格式化的Word文档
    
    Args:
        markdown_file_path: Markdown文件的路径
        output_filename: 输出的Word文件名
        
    Returns:
        tuple: (临时文件路径, 下载文件名)
    """
    import pypandoc
    
    # 创建临时输出文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as tmp:
        temp_docx_file = tmp.name
    
    # 使用Pandoc转换Markdown到Word
    logger.info(f"使用Pandoc转换文件: {markdown_file_path} 到 {temp_docx_file}")
    
    # 转换文件
    pypandoc.convert_file(
        markdown_file_path,
        'docx',
        outputfile=temp_docx_file,
        format='markdown',
        extra_args=[
            '--standalone',
            '--shift-heading-level-by=0'  # 保持原始标题级别
        ]
    )
    
    # 使用python-docx处理文档格式
    doc = Document(temp_docx_file)
    doc = format_docx_file(doc)
    doc.save(temp_docx_file)
    
    return temp_docx_file, output_filename

# 下载项目大纲的Word文档格式的接口（基于Pandoc实现）
@router.get("/{project_id}/download-outline-docx")
async def download_project_outline_as_docx(project_id: str, current_user: UserResponse = Depends(get_current_user)):
    """
    使用Pandoc下载项目大纲文件为Word文档格式
    
    Args:
        project_id: 项目ID
        current_user: 当前用户
        
    Returns:
        FileResponse: 可下载的Word格式大纲文件
    """
    try:
        import pypandoc
        
        # 检查用户是否为试用用户
        if current_user.is_trial:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="下载功能仅对付费用户开放，升级账户即可使用此功能"
            )
            
        # 检查项目是否存在并验证权限
        config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
        if not config_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目配置不存在"
            )
        
        if config_db.user.id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此项目"
            )
        
        # 检查大纲状态和文件是否存在
        if config_db.status in [ProjectConfigStatus.OUTLINE_GENERATING.value, 
                                  ProjectConfigStatus.OUTLINE_FAILED.value,
                                  ProjectConfigStatus.OUTLINE_CANCELED.value
                                  ]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="大纲尚未生成完成或生成失败"
            )
        
        # 获取大纲文件路径（优先使用人工修改的大纲）
        outline_path = config_db.manual_modified_outline or config_db.ai_generated_outline
        if not outline_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="大纲文件路径不存在"
            )
        
        # 处理文件路径
        if os.path.isabs(outline_path):
            abs_file_path = outline_path
        else:
            abs_file_path = os.path.join(os.getcwd(), outline_path)
        
        # 检查文件是否存在
        if not os.path.exists(abs_file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"大纲文件不存在: {outline_path}"
            )
        
        # 生成下载文件名
        if config_db.name:
            date_str = datetime.now().strftime("%Y-%m-%d")
            download_filename = f"outline_{config_db.name.replace(' ', '_')}_{date_str}.docx"
        else:
            download_filename = f"outline_{datetime.now().strftime('%Y-%m-%d')}.docx"
        
        # 使用通用函数处理转换
        try:
            temp_docx_file, _ = convert_markdown_to_docx(abs_file_path, download_filename)
        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文档处理失败: {str(e)}"
            )
        
        # 返回文件下载响应
        return FileResponse(
            path=temp_docx_file,
            filename=download_filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            background=BackgroundTask(lambda: os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None)
        )
    except ImportError as e:
        logger.error(f"导入pypandoc失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="需要安装pypandoc库才能使用此功能。请运行 'pip install pypandoc'"
        )
    except Exception as e:
        logger.error(f"下载Word文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载Word文档失败: {str(e)}"
        )

# 下载项目报告的Word文档格式的接口
@router.get("/{project_id}/download-report-docx")
async def download_project_report_as_docx(project_id: str, current_user: UserResponse = Depends(get_current_user)):
    """
    下载项目报告文件为Word文档格式
    
    Args:
        project_id: 项目ID
        current_user: 当前用户
        
    Returns:
        FileResponse: 可下载的Word格式报告文件
    """
    try:
        import pypandoc
        
        # 检查用户是否为试用用户
        if current_user.is_trial:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="下载功能仅对付费用户开放，升级账户即可使用此功能"
            )
            
        # 检查项目是否存在并验证权限
        config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
        if not config_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目配置不存在"
            )
        
        if config_db.user.id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此项目"
            )
        
        # 检查报告状态和文件是否存在
        if config_db.status != ProjectConfigStatus.REPORT_GENERATED.value:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="报告尚未生成完成或生成失败"
            )
        
        # 获取报告文件路径
        report_path = config_db.manual_modified_report or config_db.ai_generated_report
        if not report_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报告文件路径不存在"
            )
        
        # 处理文件路径
        if os.path.isabs(report_path):
            abs_file_path = report_path
        else:
            abs_file_path = os.path.join(os.getcwd(), report_path)
        
        # 检查文件是否存在
        if not os.path.exists(abs_file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"报告文件不存在: {report_path}"
            )
        
        # 生成下载文件名
        if config_db.name:
            date_str = datetime.now().strftime("%Y-%m-%d")
            download_filename = f"{config_db.name.replace(' ', '_')}_{date_str}.docx"
        else:
            download_filename = f"report_{datetime.now().strftime('%Y-%m-%d')}.docx"
        
        # 使用通用函数处理转换
        try:
            temp_docx_file, _ = convert_markdown_to_docx(abs_file_path, download_filename)
        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文档处理失败: {str(e)}"
            )
        
        # 返回文件下载响应
        return FileResponse(
            path=temp_docx_file,
            filename=download_filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            background=BackgroundTask(lambda: os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None)
        )
    except ImportError as e:
        logger.error(f"导入pypandoc失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="需要安装pypandoc库才能使用此功能。请运行 'pip install pypandoc'"
        )
    except Exception as e:
        logger.error(f"下载Word文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载Word文档失败: {str(e)}"
        ) 