from fastapi import APIRouter, Query, status, Depends
from typing import List, Optional
from uuid import UUID
from datetime import datetime
from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.models.role import Role
from app.api.schemas.role import RoleCreate, RoleUpdate, RoleResponse, InsetRole
from app.utils.utils import send_data, ResponseModel
from app.models.organizations import Organizations

router = APIRouter()

@router.post("", response_model=ResponseModel[RoleResponse], status_code=status.HTTP_201_CREATED)
async def create_role(
    role: RoleCreate,
    current_user: UserResponse = Depends(get_current_user)
):
    """创建新角色"""
    try:
        # 超级管理员不允许创建
        if role.identifier == InsetRole.SUPER_ADMIN:
            return send_data(False, None, f"角色标识符{InsetRole.SUPER_ADMIN.value}不允许创建")
        # 如果是超级管理员
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            organization = await Organizations.get_or_none(id=role.organization, is_deleted=False)
            if not organization:
                return send_data(False, None, f"用户机构不存在")
            db_role = await Role.create(
                name=role.name,
                identifier=role.identifier,
                organization=organization
            )
            return send_data(True, RoleResponse.model_validate(db_role, from_attributes=True))
        else:
            # 检查标识符是否已存在
            existing_role = await Role.filter(identifier=role.identifier, is_deleted=False, organization=current_user.organization.id).first()
            if existing_role:
                return send_data(False, None, f"角色标识符{InsetRole.SUPER_ADMIN}已存在")
            organization = await Organizations.get_or_none(id=current_user.organization.id, is_deleted=False)
            if not organization:
                return send_data(False, None, f"用户机构不存在")
            db_role = await Role.create(
                name=role.name,
                identifier=role.identifier,
                organization=organization
            )
            return send_data(True, RoleResponse.model_validate(db_role, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"创建角色失败: {str(e)}")

@router.get("", response_model=ResponseModel[List[RoleResponse]])
async def list_roles(
    contain_deleted: Optional[bool] = Query(False, description="是否包含被删除的数据"),
    current_user: UserResponse = Depends(get_current_user)
):
    """获取角色列表"""
    try:
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            roles = Role.filter()
            if not contain_deleted:
                roles = roles.filter(is_deleted=False)
            roles = await roles.all().order_by("-updated_at")
            return send_data(True, [RoleResponse.model_validate(role, from_attributes=True) for role in roles])
        else:
            roles = Role.filter(organization=current_user.organization.id)
            if not contain_deleted:
                roles = roles.filter(is_deleted=False)
            roles = await roles.all().order_by("-updated_at")
            return send_data(True, [RoleResponse.model_validate(role, from_attributes=True) for role in roles])
    except Exception as e:
        return send_data(False, None, f"获取角色列表失败: {str(e)}")

@router.get("/{role_id}", response_model=ResponseModel[RoleResponse])
async def get_role(
    role_id: UUID,
    current_user: UserResponse = Depends(get_current_user)
):
    """获取特定角色详情"""
    try:
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            role = await Role.filter(id=role_id, is_deleted=False).first()
            if not role:
                return send_data(False, None, "角色不存在")
            return send_data(True, RoleResponse.model_validate(role, from_attributes=True))
        else:
            role = await Role.filter(id=role_id, is_deleted=False, organization=current_user.organization.id).first()
            if not role:
                return send_data(False, None, "角色不存在")
            return send_data(True, RoleResponse.model_validate(role, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"获取角色详情失败: {str(e)}")

@router.put("/{role_id}", response_model=ResponseModel[RoleResponse])
async def update_role(
    role_id: UUID,
    role: RoleUpdate,
    current_user: UserResponse = Depends(get_current_user)
):
    """更新角色信息"""
    try:
        db_role = None
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            db_role = await Role.filter(id=role_id, is_deleted=False).first()
        else:
            db_role = await Role.filter(id=role_id, is_deleted=False, organization=current_user.organization.id).first()
        if not db_role:
            return send_data(False, None, "角色不存在")

        # 如果更新标识符，检查是否已存在
        if role.identifier and role.identifier != db_role.identifier:
            existing_role = await Role.filter(identifier=role.identifier, is_deleted=False).first()
            if existing_role:
                return send_data(False, None, "角色标识符已存在")
        # if "organization_id" in update_data:
        #     update_data.pop("organization_id")
        update_data = role.model_dump(exclude_unset=True)
        await db_role.update_from_dict(update_data)
        await db_role.save()
        return send_data(True, RoleResponse.model_validate(db_role, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"更新角色失败: {str(e)}")

@router.delete("/{role_id}", response_model=ResponseModel[RoleResponse])
async def delete_role(
    role_id: UUID,
    current_user: UserResponse = Depends(get_current_user)
):
    """删除角色（软删除）"""
    try:
        role = None
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            role = await Role.filter(id=role_id, is_deleted=False).first()
        else:
            role = await Role.filter(id=role_id, is_deleted=False, organization=current_user.organization.id).first()
        if not role:
            return send_data(False, None, "角色不存在")

        role.is_deleted = True
        role.deleted_at = datetime.now()
        await role.save()
        return send_data(True, RoleResponse.model_validate(role, from_attributes=True))
    except Exception as e:
        return send_data(False, None, f"删除角色失败: {str(e)}") 