from typing import List, Optional, Union
from app.core.logging import get_logger
from fastapi import APIRouter, Depends, status, Body
from datetime import datetime
import os
from uuid import UUID

from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.models.project_configs import ProjectConfig
from app.models.project_leaders import ProjectLeader
from app.models.research import Research
from app.models.project_member_joins import ProjectMemberJoin
from app.api.schemas.project_configs import (
    ProjectConfigCreate,
    ProjectConfigUpdate,
    ProjectConfigResponse,
    ProjectConfigResponse2,
    LanguageStyle,
    LanguageStyleText,
    LiteratureLibraryType,
    LiteratureLibraryTypeText,
    LanguageStyleResponse,
    LiteratureLibraryResponse,
    ProjectConfigStatus,
)
from app.api.schemas.project_member_joins import ProjectMemberJoinResponse
from app.utils.utils import send_data, ResponseModel
from app.api.routes.workflow import create_workflow
from app.models.model_config import ModelConfig
from app.services.product_configs_service import ProductConfigsService
from app.models.requirements_attachments_files import RequirementsAttachmentFiles

logger = get_logger(__name__)
router = APIRouter()

async def get_project_configs(
  current_user: UserResponse,
  is_deleted: Optional[int] = 0,
) -> list[ProjectConfigResponse2]:
    """获取所有项目配置（管理员接口）"""
    # 预加载基本数据
    query = ProjectConfig.all().prefetch_related(
        "user",
        "leader",
        "model",
        "user__role",
        "user__organization",
        "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district"
    ).order_by("-updated_at")
    configs = await query.filter(is_deleted=is_deleted, user=current_user.id).all()
    
    result = []
    for config in configs:
        config_dict = ProjectConfigResponse.model_validate(config, from_attributes=True).model_dump()
        
        # 查询所有相关的团队成员
        team_members = []
        if config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            
        config_dict["team_members"] = [
            ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
            for tm in team_members
        ]
        result.append(ProjectConfigResponse2.model_validate(config_dict))
    return result

# 管理员接口
# 管理员获取项目配置列表
@router.get("/list", response_model=ResponseModel[List[ProjectConfigResponse2]])
async def read_project_configs_admin(
    is_deleted: Optional[int] = 0,
    current_user: UserResponse = Depends(get_current_user)
):
    result = await get_project_configs(is_deleted=is_deleted, current_user=current_user)
    return send_data(True, result)

async def get_one_project_config(
  project_id: str
#   current_user: UserResponse
) -> Union[ProjectConfigResponse2, None]:
    """获取特定项目配置详情（管理员接口）"""
    config = await ProjectConfig.filter(id=project_id).prefetch_related(
        "leader",
        "user",
        "model",
        "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization",
    ).first()
    if config is None:
        return None
    config_dict = ProjectConfigResponse.model_validate(config, from_attributes=True).model_dump()
    # 获取所有相关的团队成员
    team_members = []
    if config.team_members:
        team_members = await ProjectMemberJoin.filter(
            join_id=config.team_members,
            is_deleted=0
        ).prefetch_related("member").all()
      
    config_dict["team_members"] = [
        ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
        for tm in team_members
    ]
    return ProjectConfigResponse2.model_validate(config_dict)

# 管理员获取指定项目配置
@router.get("/{project_id}", response_model=ResponseModel[ProjectConfigResponse2])
async def read_project_config_admin(
    project_id: str, current_user: UserResponse = Depends(get_current_user)
):
    result = await get_one_project_config(project_id)
    if result is None:
        return send_data(False, None, "项目配置不存在")
    # if result.user.id != current_user.id:
    #     return send_data(False, None, "无权访问此项目")
    return send_data(True, result)

# 管理员创建项目配置
@router.post("", response_model=ResponseModel[ProjectConfigResponse2], status_code=status.HTTP_201_CREATED)
async def create_project_config_admin(
    config_in: ProjectConfigCreate = None, current_user: UserResponse = Depends(get_current_user)
):
    """创建新的项目配置（管理员接口）"""
    try:
        # 创建项目配置
        config_data = config_in.model_dump() if config_in else {"is_deleted": 0}
        
        # 从数据中移除外键引用，避免将字符串赋值给外键字段
        leader_id = None
        if "leader" in config_data:
            leader_id = config_data.pop("leader")
            
        team_members_id = None
        if "team_members" in config_data:
            team_members_id = config_data.pop("team_members")
        model_id = None
        if "model" in config_data:
            model_id = config_data.pop("model")
        research_id = None
        if "research" in config_data:
            research_id = config_data.pop("research")
            
        # 先移除 requirements_attachments_id，等 config 保存后再处理
        requirements_attachments_id = None
        if "requirements_attachments_id" in config_data:
            requirements_attachments_id = config_data.pop("requirements_attachments_id")
            
        config = ProjectConfig(
          **config_data,
          user_id=current_user.id,
          status=ProjectConfigStatus.CONFIGURING.value
        )
        logger.info(f"config_data: {config_data}")
        
        # 保存基本配置
        await config.save()
        
        # 处理外键关系
        if leader_id:
            leader = await ProjectLeader.filter(id=leader_id).first()
            if leader:
                config.leader = leader
                await config.save()
        if model_id:
            model = await ModelConfig.filter(id=model_id).first()
            if model:
                config.model = model
                await config.save()     
        if team_members_id:
            # 对于team_members，只需要保存ID字符串，因为它是CharField而不是ForeignKey
            config.team_members = team_members_id
            await config.save()
        if research_id:
            research = await Research.filter(id=research_id).first()
            if research:
                config.research = research
                await config.save()    
        # 处理 requirements_attachments_id - 在 config 保存后处理
        if requirements_attachments_id:
            # 查询所有指定的附件文件
            requirements_attachments_files = await RequirementsAttachmentFiles.filter(id__in=requirements_attachments_id).all()
            if requirements_attachments_files:
                # 批量更新所有附件文件的 project_configs 关联
                analysis_results = []
                for file in requirements_attachments_files:
                    file.project_configs = config
                    await file.save()
                    if file.analysis_result:
                        analysis_results.append(file.analysis_result)
                # 将分析结果组装成指定格式并存储
                config.requirements_attachments = analysis_results
                await config.save()  # 确保保存更新后的config
        
        # 查询创建的配置
        created_config = await ProjectConfig.filter(id=config.id).prefetch_related(
          "user", "leader", "model","leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization"
        ).first()
        
        config_dict = ProjectConfigResponse.model_validate(created_config, from_attributes=True).model_dump()
        
        # 获取所有相关的团队成员
        team_members = []
        if created_config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=created_config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            
        config_dict["team_members"] = [
            ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
            for tm in team_members
        ]
        
        return send_data(True, ProjectConfigResponse2.model_validate(config_dict))
    except Exception as e:
        return send_data(False, None, f"创建项目配置失败: {str(e)}")

# 管理员更新项目配置
@router.put("/{project_id}", response_model=ResponseModel[ProjectConfigResponse2])
async def update_project_config_admin(
    project_id: str, config_in: ProjectConfigUpdate
):
    """更新项目配置（管理员接口）"""
    config = await ProjectConfig.filter(id=project_id).first()
    if config is None:
        return send_data(False, None, "项目配置不存在")
    
    try:
        # 处理更新字段，只更新用户提交的字段
        update_data = config_in.model_dump(exclude_unset=True, exclude_none=True)
        
        name = update_data.get("name")
        application_category = update_data.get("application_category")
        if config.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
            return send_data(False, None, "大纲生成中，无法修改")
        if config.status == ProjectConfigStatus.REPORT_GENERATING.value:
            return send_data(False, None, "报告生成中，无法修改")
        # 验证名称和申报口径是否可以更改
        if name and name != config.name and config.status != ProjectConfigStatus.CONFIGURING.value:
            return send_data(False, None, "研究主体不可以更改")
        if application_category and application_category != config.application_category and config.status != ProjectConfigStatus.CONFIGURING.value:
            return send_data(False, None, "申报口径不可以更改")
        # 验证外键关联是否存在
        if "leader" in update_data:
            leader_id = update_data.pop("leader")
            if leader_id:
                leader = await ProjectLeader.filter(id=leader_id).first()
                if not leader:
                    return send_data(False, None, "指定的申报主体不存在")
                config.leader = leader
            else:
                config.leader = None
        # 验证外键关联是否存在
        if "model" in update_data:
            model_id = update_data.pop("model")
            if model_id:
                model = await ModelConfig.filter(id=model_id).first()
                if not model:
                    return send_data(False, None, "指定的模型配置不存在")
                config.model = model
            else:
                config.model = None
        if "research" in update_data:
            research_id = update_data.pop("research")
            if research_id:
                research = await Research.filter(id=research_id).first()
                if not research:
                    return send_data(False, None, "指定的模型配置不存在")
                config.research = research
            else:
                config.research = None
        if "team_members" in update_data:
            team_members_id = update_data["team_members"]
            if team_members_id:
                # 验证team_members_id是否存在
                exists = await ProjectMemberJoin.filter(join_id=team_members_id).exists()
                if not exists:
                    return send_data(False, None, "指定的团队成员关联ID不存在")
            # 不需要删除team_members，直接更新字符串字段

        # 如果更新了is_deleted字段为1，设置deleted_at时间
        if "is_deleted" in update_data and update_data["is_deleted"] == 1:
            update_data["deleted_at"] = datetime.now()
        
        # 处理 requirements_attachments_id - 先提取出来
        requirements_attachments_id = None
        if "requirements_attachments_id" in update_data:
            requirements_attachments_id = update_data.pop("requirements_attachments_id")
        
        # 排除AI生成内容相关字段，这些字段应由专门的生成接口更新
        # 注意：status字段需要特殊处理，确保使用正确的枚举值
        special_fields = ["ai_generated_outline", "manual_modified_outline", "ai_generated_report", "status"]
        for field in special_fields:
            if field in update_data:
                del update_data[field]
        
        # 更新其他常规字段
        for key, value in update_data.items():
            setattr(config, key, value)
        
        await config.save()
        
        # 在 config 保存后处理 requirements_attachments_id
        if requirements_attachments_id:
            # 查询所有指定的附件文件
            requirements_attachments_files = await RequirementsAttachmentFiles.filter(id__in=requirements_attachments_id).all()
            if requirements_attachments_files:
                # 批量更新所有附件文件的 project_configs 关联
                analysis_results = []
                for file in requirements_attachments_files:
                    file.project_configs = config
                    await file.save()
                    if file.analysis_result:
                        analysis_results.append(file.analysis_result)
                # 将分析结果组装成指定格式并存储
                config.requirements_attachments = analysis_results
                await config.save()  # 确保保存更新后的config
        
        # 查询更新后的配置
        updated_config = await ProjectConfig.filter(id=project_id).prefetch_related(
            "user", "leader", "model",
            "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization"
        ).first()
        
        config_dict = ProjectConfigResponse.model_validate(updated_config, from_attributes=True).model_dump()
        
        # 获取所有相关的团队成员
        team_members = []
        if updated_config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=updated_config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            
        config_dict["team_members"] = [
            ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
            for tm in team_members
        ]
        
        return send_data(True, ProjectConfigResponse2.model_validate(config_dict))
    except Exception as e:
        return send_data(False, None, f"更新项目配置失败: {str(e)}")

# 管理员软删除项目配置
@router.delete("/{project_id}", response_model=ResponseModel[ProjectConfigResponse2])
async def soft_delete_project_config_admin(
    project_id: str, current_user: UserResponse = Depends(get_current_user)
):
    """软删除项目配置（管理员接口）"""
    config = await ProjectConfig.filter(id=project_id).first()
    if config is None:
        return send_data(False, None, "项目配置不存在")
    if config.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
        return send_data(False, None, "大纲生成中，无法删除")
    if config.status == ProjectConfigStatus.REPORT_GENERATING.value:
        return send_data(False, None, "报告生成中，无法删除")
    try:
        # 标记为已删除
        config.is_deleted = 1
        config.deleted_at = datetime.now()
        await config.save()
        
        # 查询已删除的配置
        deleted_config = await ProjectConfig.filter(id=project_id).prefetch_related(
            "user",
            "leader",
            "model",
            "leader__province",  # leader 的外键字段
            "leader__city",
            "leader__district",
            "user__role",
            "user__organization"
        ).first()
        
        config_dict = ProjectConfigResponse.model_validate(deleted_config, from_attributes=True).model_dump()
        
        # 获取所有相关的团队成员
        team_members = []
        if deleted_config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=deleted_config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            
        config_dict["team_members"] = [
            ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
            for tm in team_members
        ]
        
        return send_data(True, ProjectConfigResponse2.model_validate(config_dict))
    except Exception as e:
        return send_data(False, None, f"删除项目配置失败: {str(e)}")

# 更新手动修改大纲接口
@router.post("/{project_id}/update-manual-outline", response_model=ResponseModel[ProjectConfigResponse])
async def update_manual_outline(
    project_id: str, 
    manual_modified_outline: str = Body(..., embed=True),
    current_user: UserResponse = Depends(get_current_user)
):
    """手动更新项目大纲内容"""
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related(
        "leader",
        "user",
        "model",
        "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization",
    ).first()
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    
    if config_db.user.id != current_user.id:
        return send_data(False, None, "无权访问此项目")
    
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"manual_outline_{timestamp}.txt"
    if config_db.name:
      file_name = f"manual_outline_{config_db.name[:10].replace(' ', '_')}_{timestamp}.txt"
    relative_path = config_db.manual_modified_outline or f"{project_folder}/{file_name}"
    
    # 确保目录存在
    abs_folder_path = os.path.join(os.getcwd(), project_folder)
    if not os.path.exists(abs_folder_path):
        os.makedirs(abs_folder_path)
    
    # 获取绝对路径
    abs_file_path = os.path.join(os.getcwd(), relative_path)
    
    # 保存内容到文件
    with open(abs_file_path, "w", encoding="utf-8") as f:
      f.write(manual_modified_outline)
    
    # 更新项目配置
    config_db.manual_modified_outline = relative_path
    config_db.manual_modified_outline_time = datetime.now()
    config_db.ai_generated_report = None
    config_db.report_generation_time = None
    config_db.manual_modified_report_time = None
    config_db.manual_modified_report = None
    await config_db.save()
    await create_workflow(
        project_id = project_id,
        name="OUTLINE_POLISHING",
        current_user=current_user,
        content=relative_path
    )
    return send_data(True, ProjectConfigResponse.model_validate(config_db, from_attributes=True))
# 更新修改报告接口
@router.post("/{project_id}/update-report", response_model=ResponseModel[ProjectConfigResponse])
async def update_report(
    project_id: str, 
    manual_modified_report: str = Body(..., embed=True),
    current_user: UserResponse = Depends(get_current_user)
):
    """手动更新报告的内容"""
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related(
        "leader",
        "user",
        "model",
        "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization"
    ).first()
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    
    if config_db.user.id != current_user.id:
        return send_data(False, None, "无权访问此项目")
    
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"manual_report_{timestamp}.txt"
    if config_db.name:
      file_name = f"manual_report_{config_db.name[:10].replace(' ', '_')}_{timestamp}.txt"
    relative_path = f"{project_folder}/{file_name}"
    
    # 确保目录存在
    abs_folder_path = os.path.join(os.getcwd(), project_folder)
    if not os.path.exists(abs_folder_path):
        os.makedirs(abs_folder_path)
    
    # 获取绝对路径
    abs_file_path = os.path.join(os.getcwd(), relative_path)
    
    # 保存内容到文件
    with open(abs_file_path, "w", encoding="utf-8") as f:
      f.write(manual_modified_report)
    
    # 更新项目配置
    config_db.manual_modified_report = relative_path
    config_db.manual_modified_report_time = datetime.now()
    await config_db.save()
    await create_workflow(
        project_id = project_id,
        name="CONTENT_POLISHING",
        current_user=current_user,
        content=relative_path
    )
    
    return send_data(True, ProjectConfigResponse.model_validate(config_db, from_attributes=True))

@router.get("/user/latest", response_model=ResponseModel[ProjectConfigResponse2])
async def get_latest_project_config(
    current_user: UserResponse = Depends(get_current_user)
):
    """获取用户最近创建的项目配置"""
    try:
        # 查询用户最近创建的项目配置
        config = await ProjectConfig.filter(
            user_id=current_user.id,
            is_deleted=0
        ).order_by("-created_at").prefetch_related("leader", "user", "model", "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization").first()
        
        if not config:
            return send_data(False, None, "未找到项目配置")
            
        # 获取所有相关的团队成员
        team_members = []
        if config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            
        # 构建响应数据
        config_dict = ProjectConfigResponse.model_validate(config, from_attributes=True).model_dump()
        config_dict["team_members"] = [
            ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
            for tm in team_members
        ]
        
        return send_data(True, ProjectConfigResponse2.model_validate(config_dict))
    except Exception as e:
        return send_data(False, None, f"获取最近项目配置失败: {str(e)}")

# 获取语言风格的枚举值
@router.get("/dict/language-style", response_model=ResponseModel[List[LanguageStyleResponse]])
async def get_language_style():
    """获取语言风格的枚举值"""
    language_styles = [LanguageStyleResponse(value=style.value, label=LanguageStyleText[style.value]) for style in LanguageStyle]
    return send_data(True, language_styles)

# 获取文献库的枚举值
@router.get("/dict/literature-library", response_model=ResponseModel[List[LiteratureLibraryResponse]])
async def get_literature_library():
    """获取文献库的枚举值"""
    literature_libraries = [LiteratureLibraryResponse(value=library.value, label=LiteratureLibraryTypeText[library.value]) for library in LiteratureLibraryType]
    return send_data(True, literature_libraries)

# 优化项目名称接口
@router.post("/optimize-name", response_model=ResponseModel[str])
async def optimize_project_name(
    project_configs_name: str = Body(..., embed=True),
    model_config_id: Optional[UUID] = Body(None, embed=True),
    current_user: UserResponse = Depends(get_current_user)
):
    """优化项目名称"""
    service = ProductConfigsService()
    return await service.optimize_project_name(project_configs_name, current_user, model_config_id)
