from pydantic import BaseModel, UUID4
from typing import Optional, List
from datetime import datetime


class KnowledgeCanvasBase(BaseModel):
    """知识画布基础模型"""
    name: str  # 名称
    source_type: str  # 来源类型
    type: str  # 类型
    summary: str  # 概要
    key_notes: str  # 重点注释
    related_notes: str  # 关联笔记
    ai_questions: str  # AI提问
    image_url: Optional[str] = None  # 图片URL


class KnowledgeCanvasCreate(KnowledgeCanvasBase):
    """创建知识画布请求模型"""
    pass


class KnowledgeCanvasUpdate(BaseModel):
    """更新知识画布请求模型"""
    name: Optional[str] = None
    source_type: Optional[str] = None
    type: Optional[str] = None
    summary: Optional[str] = None
    key_notes: Optional[str] = None
    related_notes: Optional[str] = None
    ai_questions: Optional[str] = None
    image_url: Optional[str] = None


class KnowledgeCanvasInDB(KnowledgeCanvasBase):
    """数据库中的知识画布模型"""
    id: UUID4  # 唯一标识符
    created_at: datetime  # 创建时间
    updated_at: datetime  # 更新时间
    is_deleted: bool  # 是否删除

    class Config:
        orm_mode = True


class KnowledgeCanvasResponse(KnowledgeCanvasInDB):
    """知识画布响应模型"""
    pass


class KnowledgeCanvasQueryParams(BaseModel):
    """知识画布查询参数"""
    keyword: Optional[str] = None  # 关键词搜索
    source_type: Optional[str] = None  # 来源类型
    type: Optional[str] = None  # 类型
    page: int = 1  # 页码
    page_size: int = 10  # 每页数量
    sort_field: Optional[str] = None  # 排序字段
    sort_order: Optional[str] = None  # 排序方式 