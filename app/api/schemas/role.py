from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from uuid import UUID
from enum import Enum

class InsetRole(str, Enum):
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"

PERMISSION_LEVELS = {
  "super_admin": 3,
  "admin": 2,
  # 其他角色都是0
}
class RoleBase(BaseModel):
    name: str = Field(..., description="角色名称")
    identifier: str = Field(..., description="角色标识符")


class RoleCreate(RoleBase):
    organization: Optional[UUID] = None

class RoleUpdate(BaseModel):
    name: Optional[str] = Field(None, description="角色名称")
    identifier: Optional[str] = Field(None, description="角色标识符")
    class Config:
        extra = 'forbid'

class RoleResponse(RoleBase):
    id: UUID
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None

    class Config:
        from_attributes = True 