from typing import Optional, Union
from uuid import UUID
from pydantic import BaseModel, Field
from datetime import datetime, timezone


class UserReportUsageBase(BaseModel):
    """用户报告使用次数基础模型"""
    used_count: int = Field(default=0, description="已使用生成报告次数")
    max_allowed_count: Optional[int] = Field(default=None, description="最大允许生成报告次数")


class UserReportUsageCreate(UserReportUsageBase):
    """创建用户报告使用次数请求模型"""
    user_id: Optional[str] = Field(None, description="用户ID")
    max_allowed_count: Optional[int] = Field(default=None, description="最大允许生成报告次数")


class UserReportUsageResponse(UserReportUsageBase):
    """用户报告使用次数响应模型"""
    id: UUID = Field(..., description="用户报告使用次数ID")
    user_id: str = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    is_deleted: bool = Field(default=False, description="是否删除")
    is_create_reports: bool = Field(default=True, description="是否可以创建报告")

    model_config = {
        "from_attributes": True
    }