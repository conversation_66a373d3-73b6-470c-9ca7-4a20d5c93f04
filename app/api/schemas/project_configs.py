from pydantic import BaseModel, UUID4
from typing import Optional, List
from datetime import datetime, timezone
from app.api.schemas.project_leaders import ProjectLeaderResponse
from app.api.schemas.project_member_joins import ProjectMemberJoinResponse
from app.api.schemas.user import UserResponse
from enum import Enum
from app.api.schemas.model_config import ModelConfigResponse


# class ApplicationCategory(str, Enum):
#   NSFC = "NSFC"  # 国自然基金-面上项目
# class ApplicationCategoryText(str, Enum):
#   NSFC = "国自然基金"

class LanguageStyle(str, Enum):
  """语言风格枚举"""

  """专业严谨"""
  PROFESSIONAL_RIGOROUS = "PROFESSIONAL_RIGOROUS"
  """正式规范"""
  FORMAL_NORMS = "FORMAL_NORMS"
  """学术权威"""
  ACADEMIC_AUTHORITY = "ACADEMIC_AUTHORITY"
  
class LanguageStyleText(str, Enum):
  PROFESSIONAL_RIGOROUS = "专业严谨"
  FORMAL_NORMS = "正式规范"
  ACADEMIC_AUTHORITY = "学术权威"

class LanguageStylePrompt(str, Enum):
  PROFESSIONAL_RIGOROUS = "Language style, default professional and rigorous"
  FORMAL_NORMS = "Language style, default formal norms"
  ACADEMIC_AUTHORITY = "Language style, default academic authority"

class ProjectConfigStatus(str, Enum):
  """项目配置状态枚举"""
  """配置中"""
  CONFIGURING = "CONFIGURING"
  """生成大纲中"""
  OUTLINE_GENERATING = "OUTLINE_GENERATING"
  """大纲生成完成"""
  OUTLINE_GENERATED = "OUTLINE_GENERATED"
  """大纲生成失败"""
  OUTLINE_FAILED = "OUTLINE_FAILED"
  """大纲生成被终止"""
  OUTLINE_CANCELED = "OUTLINE_CANCELED"
  """报告生成中"""
  REPORT_GENERATING = "REPORT_GENERATING"
  """报告生成完成"""
  REPORT_GENERATED = "REPORT_GENERATED"
  """报告生成失败"""
  REPORT_FAILED = "REPORT_FAILED"
  """报告生成被终止"""
  REPORT_CANCELED = "REPORT_CANCELED"

class LiteratureLibraryType(str, Enum):
  """文献库类型枚举"""
  """PubMed医学文献库"""
  PUBMED = "PUBMED"
  """Cell期刊"""
  CELL = "CELL"
  """Nature期刊"""
  NATURE = "NATURE"
  """Science期刊"""
  SCIENCE = "SCIENCE"
  @classmethod
  def get_url(cls, library_type: str) -> str:
    """获取文献库对应的URL"""
    url_map = {
      cls.CELL.value: "www.cell.com",
      cls.NATURE.value: "www.nature.com",
      cls.SCIENCE.value: "www.science.org"
    }
    return url_map.get(library_type, "")

class LiteratureLibraryTypeText(str, Enum):
  """文献库类型枚举"""
  """PubMed医学文献库"""
  PUBMED = "PubMed"
  """Cell期刊"""
  CELL = "Cell"
  """Nature期刊"""
  NATURE = "Nature"
  """Science期刊"""
  SCIENCE = "Science"
  

class ProjectConfigBase(BaseModel):
    name: Optional[str] = None
    application_category: Optional[str] = None  
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    status: Optional[str] = None
    team_introduction: Optional[str] = None

class LanguageStyleResponse(BaseModel):
   value: LanguageStyle
   label: str

class LiteratureLibraryResponse(BaseModel):
   value: LiteratureLibraryType
   label: str

class ProjectConfigCreate(BaseModel):
    name: Optional[str] = None
    application_category: Optional[str] = None  
    leader: Optional[str] = None
    research: Optional[str] = None
    team_members: Optional[str] = None
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    team_introduction: Optional[str] = None
    model: Optional[str] = None
    requirements_attachments_id: Optional[List[UUID4]] = None


class ProjectConfigResponse(BaseModel):
    id: UUID4
    name: Optional[str] = None
    application_category: Optional[str] = None  
    leader: Optional[ProjectLeaderResponse] = None
    user: Optional[UserResponse] = None
    team_members: Optional[str] = None
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    ai_generated_outline: Optional[str] = None
    manual_modified_outline: Optional[str] = None
    ai_generated_report: Optional[str] = None
    report_generation_time: Optional[datetime] = None
    outline_generated_time: Optional[datetime] = None
    manual_modified_outline_time: Optional[datetime] = None
    manual_modified_report_time: Optional[datetime] = None
    outline_tokens_consumed: Optional[int] = None
    report_tokens_consumed: Optional[int] = None
    outline_generated_time: Optional[datetime] = None
    manual_modified_report: Optional[str] = None
    status: Optional[ProjectConfigStatus] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_deleted: int = 0
    deleted_at: Optional[datetime] = None
    outline_tokens_consumed: Optional[int] = None
    report_tokens_consumed: Optional[int] = None
    team_introduction: Optional[str] = None
    model: Optional[ModelConfigResponse] = None

    model_config = {
      "from_attributes": True
    }
class ProjectConfigResponse2(BaseModel):
    id: UUID4
    name: Optional[str] = None
    application_category: Optional[str] = None  
    leader: Optional[ProjectLeaderResponse] = None
    user: Optional[UserResponse] = None
    team_members: Optional[List[ProjectMemberJoinResponse]] = None
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    ai_generated_outline: Optional[str] = None
    manual_modified_outline: Optional[str] = None
    ai_generated_report: Optional[str] = None
    report_generation_time: Optional[datetime] = None
    outline_generated_time: Optional[datetime] = None
    status: Optional[ProjectConfigStatus] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_deleted: int = 0
    deleted_at: Optional[datetime] = None
    outline_tokens_consumed: Optional[int] = None
    report_tokens_consumed: Optional[int] = None
    team_introduction: Optional[str] = None
    model: Optional[ModelConfigResponse] = None
    requirements_attachments_id: Optional[List[UUID4]] = None
    
    model_config = {
      "from_attributes": True
    }

class ProjectConfigUpdate(BaseModel):
    name: Optional[str] = None
    application_category: Optional[str] = None
    leader: Optional[str] = None
    research: Optional[str] = None
    team_members: Optional[str] = None
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    team_introduction: Optional[str] = None
    # ai_generated_outline: Optional[str] = None
    # manual_modified_outline: Optional[str] = None
    # ai_generated_report: Optional[str] = None
    report_generation_time: Optional[datetime] = None
    status: Optional[ProjectConfigStatus] = None
    is_deleted: Optional[int] = None
    deleted_at: Optional[datetime] = None 
    model: UUID4
    requirements_attachments_id: Optional[List[UUID4]] = None