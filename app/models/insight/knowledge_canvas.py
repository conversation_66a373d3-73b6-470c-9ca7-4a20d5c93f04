import uuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime
from app.models.insight.knowledge_canvas_tag import KnowledgeCanvasTag


class KnowledgeCanvas(Model):
    """知识画布模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(max_length=255, description="名称")
    source_type = fields.CharField(max_length=100, description="来源类型")
    type = fields.CharField(max_length=100, description="类型")
    summary = fields.TextField(description="概要")
    key_notes = fields.TextField(description="重点注释")
    related_notes = fields.TextField(description="关联笔记")
    ai_questions = fields.TextField(description="AI提问")
    image_url = fields.CharField(max_length=500, null=True, description="图片URL")
    original_article = fields.TextField(description="原始文章")
    ai_outline = fields.TextField(description="AI大纲")
    user_notes = fields.TextField(description="用户注释")
    note_update_at = fields.DatetimeField(null=True, description="笔记修改时间")
    user = fields.ForeignKeyField(
        "models.User", 
        related_name="knowledge_canvases", 
        null=True, 
        description="创建用户"
    )
    tags = fields.ManyToManyField(
        "models.KnowledgeCanvasTag",
        related_name="knowledge_canvases",
        through="knowledge_canvas_tag_relation",
        description="标签"
    )
    
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    
    class Meta:
        table = "knowledge_canvas"
        description = "知识画布" 