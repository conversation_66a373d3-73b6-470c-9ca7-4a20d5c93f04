from fastapi import Request
from fastapi.exceptions import RequestValidationError, HTTPException
from fastapi.responses import JSONResponse
# from fastapi.exception_handlers import request_validation_exception_handler
from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY, HTTP_401_UNAUTHORIZED, HTTP_500_INTERNAL_SERVER_ERROR
# from app.utils.utils import send_data

async def custom_validation_exception_handler(request: Request, exc: RequestValidationError):
  errorMsg = ""
  try:  
    error = exc.errors()
    print(error)
    for item in error:
      type = item.get("type")
      if type == 'missing':
        field = item.get("loc")[1]
        # print(item.get('loc')[1])
        errorMsg += f"，缺少{field}字段" if errorMsg else f"缺少{field}字段"
      elif type == 'value_error':
        field = item.get("ctx").get("error")
        errorMsg += f"，{field}" if errorMsg else f"{field}"
      elif type == 'extra_forbidden':
        field = item.get("loc")[1]
        msg = item.get("msg")
        errorMsg += f"，【{field}】{msg}" if errorMsg else f"【{field}】{msg}"
  except:
    errorMsg += "未知的验证错误"
  return JSONResponse(
    status_code=HTTP_422_UNPROCESSABLE_ENTITY,
    content = {
      "code": 422,
      "error": errorMsg,
      "data": None,
      "success": False
    })
async def custom_no_auth_handler(request: Request, exc: HTTPException):
  status = exc.status_code
  if status == 401:
    return JSONResponse(
      status_code=HTTP_401_UNAUTHORIZED,
      content = {
        "code": 401,
        "error": "无效的认证凭据",
        "data": None,
        "success": False
      })
  else:
    return JSONResponse(
      status_code=HTTP_500_INTERNAL_SERVER_ERROR,
      content = {
        "code": 500,
        "error": exc.detail,
        "data": None,
        "success": False
      })