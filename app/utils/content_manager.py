from typing import Dict, List, Optional
import asyncio
from enum import Enum
import re
# import asyncio

# init_data = """好的，作为一名专业的项目申报材料撰写专家，我将为您生成一份针对国家自然科学基金（NSFC）口径，关于"口腔溃疡发病原理"研究的完整项目申报大纲。这份大纲将整合您提供的背景信息，并确保结构严谨、要素齐全，符合NSFC的要求，为撰写不少于2000字的详细报告奠定基础。

# **国家自然科学基金项目申请书 - 撰写大纲**
# **(项目主题：口腔溃溃发病原理)**

# ---

# **封面页 / 简表信息 (Cover Page / Basic Information)**

# *   **项目名称 (Project Title):** (需精准体现研究内容和创新点，例如："基于多组学联合分析的复发性阿弗他溃疡免疫失衡与微生物互作机制研究" 或 "口腔溃疡发生发展中特定信号通路的关键作用及调控机制探究" - *待根据具体研究内容拟定*)
# *   **申请代码 (Subject Code):** (根据研究内容选择NSFC医学科学部等相关学科代码)
# *   **研究类型 (Research Category):** (例如：面上项目、青年科学基金项目等 - *根据申请人和团队情况选择*)
# *   **亚类说明 (Sub-category):** (如适用)
# *   **附注说明 (Notes):** (如适用)
# *   **申请人信息 (Applicant Information):**
#     *   姓名 (Name): [项目负责人姓名，如张六教授]
#     *   依托单位 (Host Institution): 瑞京科技 (需确认该公司是否具备NSFC依托单位资质，若无，通常需联合具备资质的高校或科研院所申报，例如以上海科技大学作为依托单位，瑞京科技作为合作单位或参与单位)
#     *   合作单位 (Collaborating Institution): 上海科技大学，上海市长海医院 (需明确各单位职责分工)
# *   **项目组成员 (Team Members):** 张六教授 (上海科技大学)，张五副教授 (上海科技大学)，[瑞京科技及长海医院相关人员] (需列出所有核心成员及其单位、职称、研究专长)
# *   **项目起止年月 (Project Start/End Date):** (通常为3-4年)
# *   **申请经费 (Budget Request):** (根据研究需要和基金资助强度合理测算)

# ---

# **摘要 (Abstract) (限400字)**

# 1.  **研究背景:** 简述口腔溃疡的临床普遍性、危害性以及当前对其发病机制认识的不足和争议。
# 2.  **科学问题:** 凝练本项目拟解决的核心科学问题 (例如：免疫失衡、遗传易感、微生物因素在口腔溃疡发生中的具体作用机制及其相互关系)。
# 3.  **研究内容:** 概述本项目将从哪些方面（如免疫细胞亚群、特定信号通路、口腔微生物组、遗传多态性等）开展研究。
# 4.  **研究方法:** 简要说明将采用的关键技术和研究手段 (如临床样本收集、多"""
class ContentStatus(str, Enum):
    """内容状态"""
    ERROR = "error"  # 正在生成
    NORMAL = "NORMAL"  # 生成完成
class Data:
    """数据结构"""
    def __init__(self, content: str, status: ContentStatus = ContentStatus.NORMAL):
        self.status: ContentStatus = status  # 已读取的内容块
        self.content: str = content  # 未读取的内容块 # 异步任务实例

class ProjectContent:
    """项目内容数据结构"""
    def __init__(self):
        # self.status: ContentStatus = ContentStatus.GENERATING
        self.read_chunks: List[Data] = []  # 已读取的内容块
        self.unread_chunks: List[Data] = []  # 未读取的内容块
        self.error_message: Optional[str] = None  # 错误信息
        self.asyncioInstance: Optional[asyncio.Task] = None  # 异步任务实例
#     # self.error_message: Optional[str] = None  # 错误信息

class ContentManager:
    """内容管理器，用于管理项目内容的读写状态"""
    _instance = None
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(ContentManager, cls).__new__(cls)
            cls._instance.data = {}
            # cls._instance.init_with_predefined_data()
        return cls._instance
    
    data: Dict[str, ProjectContent]
    
    def add_content(self, project_id: str, content: str, status: ContentStatus = ContentStatus.NORMAL) -> None:
        """
        添加内容到指定项目的未读取队列
        
        Args:
            project_id: 项目ID
            content: 要添加的内容
        """
        # 确保项目ID存在于数据字典中
        if project_id not in self.data:
            self.data[project_id] = ProjectContent()
        print(f"添加内容到未读取队列: {content}")
        # 添加内容到未读取队列
        self.data[project_id].unread_chunks.append(Data(content, status))
    
    def add_asyncio(self, project_id: str, asyncioInstance: asyncio.Task) -> None:
        """
        为指定项目添加asyncio任务实例
        
        Args:
            project_id: 项目ID
            asyncioInstance: asyncio任务实例
        """
        # 确保项目ID存在于数据字典中
        if project_id not in self.data:
            self.data[project_id] = ProjectContent()
        # 存储asyncio任务实例
        self.data[project_id].asyncioInstance = asyncioInstance
        print(f"为项目 {project_id} 添加asyncio任务实例")
    
    def read_next_chunk(self, project_id: str) -> Optional[Data]:
        """
        读取指定项目的下一个未读取内容块
        
        Args:
            project_id: 项目ID
            
        Returns:
            下一个未读取的内容块，如果没有则返回None
        """
        # 检查项目ID是否存在
        if project_id not in self.data:
            return None
        
        project_content = self.data[project_id]
        
        # 检查是否有未读取的内容
        if not project_content.unread_chunks:
            return None
        
        # 从未读取队列中取出第一项
        chunk = project_content.unread_chunks.pop(0)
        
        # 添加到已读取队列
        project_content.read_chunks.append(chunk)
        
        # 如果取出来的是错误状态的信息。那么将本地缓存数据全部清除
        if chunk.status == ContentStatus.ERROR:
            # 删除项目ID
            del self.data[project_id]
        return chunk
    
    def get_project_content(self, project_id: str) -> Optional[ProjectContent]:
        """
        获取项目内容对象
        
        Args:
            project_id: 项目ID
            
        Returns:
            项目内容对象，如果不存在则返回None
        """
        return self.data.get(project_id)
    
    def clear_project(self, project_id: str) -> None:
        """
        清除项目的所有内容
        
        Args:
            project_id: 项目ID
        """
        if project_id in self.data:
            del self.data[project_id]
    
    def save_all_content_to_file(self, project_id: str, file_path: str) -> bool:
        """
        将项目的所有内容（已读取和未读取）保存到指定文件
        
        Args:
            project_id: 项目ID
            file_path: 要保存到的文件路径
            
        Returns:
            是否保存成功
        """
        # 检查项目ID是否存在
        if project_id not in self.data:
            return False
        
        try:
            # 处理文件路径
            import os
            
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path and not os.path.exists(dir_path):
                os.makedirs(dir_path)
            
            # 合并已读取和未读取的内容
            project_content = self.data[project_id]
            all_chunks = project_content.read_chunks + project_content.unread_chunks
            full_content = "".join([chunk.content for chunk in all_chunks])
            # 写入文件
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(self.formatRelations(full_content))
                # f.write(full_content)
            return True
        except Exception as e:
            print(f"保存内容到文件出错: {str(e)}")
            return False
    def formatRelations(self, text: str) -> str:
        """
        格式化参考文献
        """
        
        pattern = r'(^\[\d+\])\s(.+?)\s(http[s]?://[^\s]+)'
        return re.sub(pattern, r'\1 [\2](\3)', text, flags=re.MULTILINE)
    # async def init_with_predefined_data(self, project_id: str = "0dd1d420-1738-430b-a422-59df9dd883c0") -> None:
    #     """
    #     将预定义的init_data按行分割，存入指定项目ID的未读取数组
    #     """
        
    #     # 确保项目ID存在于数据字典中
    #     if project_id not in self.data:
    #         self.data[project_id] = ProjectContent()
        
    #     # 按换行符分割内容
    #     lines = init_data.split('\n')
        
    #     # 将每行内容添加到未读取数组，每次添加后延时0.5秒
    #     for line in lines:
    #         # 添加换行符，以保持原始格式
    #         self.data[project_id].unread_chunks.append(line + '\n')
    #         # 延时0.5秒
    #         await asyncio.sleep(0.5)
        
    #     print(f"初始化完成: 已将{len(lines)}行内容添加到项目{project_id}的未读取数组")
