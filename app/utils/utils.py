from typing import Generic, TypeVar, Optional, AsyncGenerator
from pydantic.generics import GenericModel
import os
import json
# import asyncio
import re
from pydantic import BaseModel

class Reference(BaseModel):
  index: int
  authors: str
  title: str
  journal: str
  year: str
  volume: int|None
  issue: int|None
  pages: str|None

T = TypeVar("T")

class ResponseModel(GenericModel, Generic[T]):
  success: bool
  code: int
  data: Optional[T] = None
  error: Optional[str] = ""# from fastapi.responses import JSONResponse

def send_data(is_success: bool, data: Optional[T], error: str = "") -> ResponseModel[T]: 
  return ResponseModel(
    success=is_success,
    code=0 if is_success else 500,
    data=data if is_success else None,
    error=error if not is_success else ""
  )

def save_text_to_file(content: str, file_path: str) -> str:
  """
  将字符串内容保存或追加到指定文件中
  
  Args:
      content: 要保存的文本内容
      file_path: 文件的相对路径
      
  Returns:
      保存的文件相对路径
  """
  # 获取绝对路径
  abs_path = os.path.join(os.getcwd(), file_path)
  
  # 确保目录存在
  directory = os.path.dirname(abs_path)
  if directory and not os.path.exists(directory):
    os.makedirs(directory)
    
  # 追加内容到文件（如果文件不存在则创建）
  with open(abs_path, "a", encoding="utf-8") as f:
    f.write(content)
    
  # 返回相对路径
  return file_path

# 辅助函数：流式读取文件内容（SSE格式）
async def stream_file_content_sse(file_path: str) -> AsyncGenerator[str, None]:
    """
    流式读取文件内容并以SSE格式返回
    
    Args:
        file_path: 文件的相对路径或绝对路径
        
    Yields:
        SSE格式的文件内容
    """
    # 处理文件路径
    if os.path.isabs(file_path):
        abs_file_path = file_path
    else:
        abs_file_path = os.path.join(os.getcwd(), file_path)
    
    # 检查文件是否存在
    if not os.path.exists(abs_file_path):
        yield f"data: {json.dumps({'error': f'文件不存在: {file_path}'})}\n\n"
        return
    with open(abs_file_path, 'r', encoding='utf-8') as f:
      content = f.read()
      extract_data(content) 
    try:
        # 每次读取的块大小
        chunk_size = 20
        
        with open(abs_file_path, 'r', encoding='utf-8') as f:
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                
                # 转换为SSE格式
                yield f"data: {json.dumps({'content': chunk})}\n\n"
                # 短暂暂停，控制流速
                # await asyncio.sleep(0.02)
        
        # 发送完成事件
        yield f"data: {json.dumps({'status': 'completed'})}\n\n"
    
    except Exception as e:
        yield f"data: {json.dumps({'error': f'读取文件时出错: {str(e)}'})}\n\n"
def extract_data(text: str):
  pattern = re.compile(
    r"""
    ^\[(?P<index>\d+)\]\s+                                       # 编号，如 [76]
    (?P<authors>.+?)\.                                # 作者，直到第一个句号
    \s+(?P<title>.+?)\.                               # 标题，直到下一个句号
    \s+(?P<journal>.+?)\.                             # 期刊或出处
    \s+(?P<year>\d{4})                                # 年份
    (?:;(?P<volume>\d+)(?:\((?P<issue>\d+)\))?        # 卷号（期号）
    :(?P<pages>[\d\-–—]+))?                           # 页码（可选）
    [。.]?$                                           # 可有结尾句号/中文句号
    """,
    re.VERBOSE
  )
  result_list:list[Reference] = []
  print('match', pattern.finditer(text))
  for match in pattern.finditer(text):
    result = match.groupdict()
    print(result)
    reference = Reference(**result)
    result_list.append(reference)
  return result_list