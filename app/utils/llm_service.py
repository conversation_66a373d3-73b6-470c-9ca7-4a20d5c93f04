import json
import aiohttp
import asyncio
import traceback
from typing import Dict, List, Optional, Callable
import os

from app.core.config import settings
from app.core.logging import get_logger

# 获取logger实例
logger = get_logger(__name__)

async def stream_llm_and_save(
    messages: List[Dict[str, str]],
    apiKey: str = "",
    apiUrl: str = "",
    model: str = "",
    callback: Optional[Callable[[str], None]] = None,
    complete_callback: Optional[Callable[[str], None]] = None,
    error_callback: Optional[Callable[[str], None]] = None,
) -> str:
    """
    流式调用LLM，实时保存响应内容到文件，并返回完整响应
    
    Args:
        messages: 聊天消息列表
        model: 模型名称
        callback: 可选的回调函数，每接收到一个chunk就调用一次
        complete_callback: 可选的完成回调函数，生成完成时调用
    
    Returns:
        完整的模型响应文本
    """
    logger.info(f"流式调用LLM并保存到文件，模型: {model}")
    if not apiKey:
        logger.error("未设置API_KEY环境变量")
        raise ValueError("未设置API_KEY环境变量")
    logger.info(f"开始请求API: {model}")
    headers = {
        "Authorization": f"Bearer {apiKey}",
        "X-Title": "Hi-IdeaGen",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": messages,
        "stream": True
        # "max_tokens": 1000
    }
    
    # 设置超时
    timeout = aiohttp.ClientTimeout(total=3600)  # 30分钟
    logger.debug(f"流式LLM请求，超时设置: {timeout.total}秒，消息数量: {len(messages)}")
    
    try:
        full_response = ""
        async with aiohttp.ClientSession() as session:
            logger.debug(f"向OpenRouter发送流式请求: {settings.OPENROUTER_URL}")
            async with session.post(
                apiUrl,
                headers=headers,
                json=payload,
                timeout=timeout
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    error_msg = f"OpenRouter API 错误: 状态码 {resp.status}, 错误详情: {error_text}"
                    logger.error(error_msg)
                    if error_callback:
                        await error_callback(error_msg)
                    return f"API请求错误: {error_msg}"
                
                # 流式响应并写入文件
                logger.debug(f"开始接收流式LLM响应并写入文件")
                chunk_count = 0
                response_debug = []  # 用于调试的响应样本
                open_router_id: Optional[str] = None
                # 使用追加模式写入文件
                try:
                    async for line in resp.content:
                        try:
                            line_str = line.decode('utf-8').strip()
                            if not line_str:
                                continue
                            
                            # 收集一些样本用于调试
                            if len(response_debug) < 3:
                                response_debug.append(line_str)
                            
                            # 跳过 "data: " 前缀
                            if line_str.startswith("data: "):
                                line_str = line_str[6:]
                            
                            # 处理流结束标记
                            if line_str == "[DONE]":
                                logger.debug("流式响应结束")
                                break
                            # 解析 JSON 数据
                            try:
                                data = json.loads(line_str)
                                open_router_id = data.get("id")
                                # print(open_router_id)
                                # 提取内容增量
                                if 'choices' in data and len(data['choices']) > 0:
                                    delta = data['choices'][0].get('delta', {})
                                    if 'content' in delta and delta['content']:
                                        chunk = delta['content']
                                        full_response += chunk
                                        # 如果有回调函数，则调用
                                        if callback:
                                            callback(chunk)
                                        
                                        chunk_count += 1
                            except json.JSONDecodeError as je:
                                logger.error(f"无法解析 JSON '{line_str[:100]}...': {str(je)}")
                            except Exception as e:
                                logger.error(f"处理响应数据时出错: {str(e)}, 数据: {line_str[:100]}...")
                        except UnicodeDecodeError as ue:
                            logger.error(f"解码响应时出错: {str(ue)}")
                        except Exception as e:
                            logger.error(f"处理响应行时出错: {str(e)}")
                            
                except Exception as e:
                    logger.error(f"读取响应流时出错: {str(e)}")
                
                logger.info(f"流式响应完成，接收了 {chunk_count} 个数据块，总长度: {len(full_response)}")
                
                # 如果没有收到任何内容，记录更多的调试信息
                if not full_response:
                    result = f"没有接收到有效内容。响应样本: {response_debug}"
                    logger.warning(result)
                    if error_callback:
                        await error_callback(result)
                    # 写入一些信息到文件，表明请求成功但没有收到内容
                    return f"请求完成，但未收到有效内容"
                
                # 调用完成回调
                if complete_callback:
                    try:
                        await complete_callback(open_router_id)
                    except Exception as e:
                        logger.error(f"执行完成回调时出错: {str(e)}")
                
                return full_response
                    
    except asyncio.TimeoutError as te:
        error_msg = f"OpenRouter API 请求超时 ({timeout.total} 秒)"
        logger.error(error_msg)
        return f"请求超时: {error_msg}"
    except aiohttp.ClientError as ce:
        error_msg = f"HTTP客户端错误: {str(ce)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"调用OpenRouter时发生错误: {str(e)}"
        logger.error(error_msg)
        logger.debug(f"错误详情:\n{traceback.format_exc()}")
        return error_msg 
    except asyncio.CancelledError:
        logger.warning("请求被取消")
        return "请求被取消"